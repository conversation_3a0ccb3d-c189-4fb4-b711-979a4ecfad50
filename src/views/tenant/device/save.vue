<template>
    <el-dialog
        :title="mode === 'add' ? '添加设备' : mode === 'edit' ? '编辑设备' : '查看设备'"
        :close-on-click-modal="false"
        :destroy-on-close="true"
        v-model="visible"
        width="600px"
    >
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="120px"
            class="form-container"
        >
            <el-form-item label="设备编码" prop="code">
                <el-input v-model="form.code" placeholder="请输入设备编码" :disabled="mode === 'show'" />
            </el-form-item>

            <el-form-item label="设备名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入设备名称" :disabled="mode === 'show'" />
            </el-form-item>

            <el-form-item label="层位数" prop="level">
                <el-input-number v-model="form.level" :min="1" :disabled="mode === 'show'" />
            </el-form-item>

            <el-form-item label="所在位置" prop="location">
                <el-input v-model="form.location" placeholder="请选择位置" :disabled="mode === 'show'" />
            </el-form-item>

            <el-form-item label="油井深度(米)" prop="depth">
                <el-input-number v-model="form.depth" :min="0" :precision="2" :disabled="mode === 'show'" />
            </el-form-item>

            <el-form-item label="设备状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择状态" :disabled="mode === 'show'">
                    <el-option label="停用" :value="0" />
                    <el-option label="正常" :value="1" />
                    <el-option label="异常" :value="2" />
                    <el-option label="维修中" :value="3" />
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="close">取消</el-button>
                <el-button v-if="mode !== 'show'" type="primary" @click="submitForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
export default {
    name: 'DeviceSave',
    data() {
        return {
            mode: 'add',
            visible: false,
            form: {
                id: '',
                code: '',
                name: '',
                level: 1,
                location: '',
                depth: 0,
                status: 1
            },
            rules: {
                code: [{ required: true, message: '请输入设备编码', trigger: 'blur' }],
                name: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
                level: [{ required: true, message: '请输入层位数', trigger: 'blur' }],
                status: [{ required: true, message: '请选择状态', trigger: 'change' }]
            }
        }
    },
    methods: {
        show(mode = 'add') {
            this.mode = mode
            this.visible = true
            return this
        },
        close() {
            this.visible = false
            this.$emit('saveDialogClosedEmit')
        },
        setData(row) {
            this.form = { ...row }
            return this
        },
        async submitForm() {
            try {
                const valid = await this.$refs.form.validate()
                if (valid) {
                    if (this.mode === 'add') {
                        await this.$API.tenant.device.add.post(this.form)
                        this.$message.success('添加成功')
                    } else {
                        await this.$API.tenant.device.edit.put(this.form)
                        this.$message.success('修改成功')
                    }
                    this.$emit('saveSuccessEmit', this.form, this.mode)
                    this.close()
                }
            } catch (error) {
                console.error('表单提交失败:', error)
            }
        }
    }
}
</script>
