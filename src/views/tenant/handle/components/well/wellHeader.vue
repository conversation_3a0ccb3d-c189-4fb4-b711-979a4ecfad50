<template>
    <block class="header-left">
        <span>翻屏显示：</span>
        <el-button-group>
            <el-tooltip effect="dark" :content="currentPage <= 1 ? '没有了' : '上一屏'" placement="bottom">
                <el-button
                    type="primary"
                    color="#662f8f"
                    round
                    size="small"
                    :disabled="currentPage <= 1"
                    @click="prevPage"
                    icon="el-icon-d-arrow-left"
                >
                </el-button>
            </el-tooltip>
            <el-tooltip effect="dark" :content="currentPage >= pageTotal ? '没有了' : '下一屏'" placement="bottom">
                <el-button
                    type="primary"
                    color="#662f8f"
                    round
                    size="small"
                    :disabled="currentPage >= pageTotal"
                    @click="nextPage"
                    icon="el-icon-d-arrow-right"
                >
                </el-button>
            </el-tooltip>
        </el-button-group>
    </block>
    <block class="header-right">
        <wellInfo :wellInfo="wellInfo"></wellInfo>
    </block>
</template>

<script>
import wellInfo from "./wellInfo.vue";

export default {
    name: 'wellHeader',
    components: {
        wellInfo
    },
    props: {
        // 当前油井信息
        wellInfo: {
            type: Object,
            required: true,
            default: () => ({
                id: 0,
                name: '',
                level: 0
            })
        },
        // 当前油井屏幕分页信息
        pageInfo: {
            type: Object,
            required: true,
            default: () => ({
                currentPage: 1,
                pageShow: 4,
                pageTotal: 1
            })
        }
    },
    data() {
        return {
            currentPage: this.pageInfo.currentPage || 1,
            pageTotal: this.pageInfo.pageTotal || 1,
        };
    },
    watch: {
        pageInfo: {
            handler(val) {
                this.currentPage = val.currentPage || 1;
                this.pageTotal = val.pageTotal || 1;
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        /**
         * 翻页上一屏
         */
        prevPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.updatePageInfo();
            }
        },
        /**
         * 翻页下一屏
         */
        nextPage() {
            if (this.currentPage < this.pageTotal) {
                this.currentPage++;
                this.updatePageInfo();
            }
        },
        /**
         * 更新分页信息
         */
        updatePageInfo() {
            this.$router.push({
                query: {
                    ...this.$route.query,
                    page: this.currentPage.toString()
                }
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.header-left {
    display: flex;
    align-items: center;
}

.header-right {
    display: flex;
    align-items: center;
    color: #666;
}
</style>
