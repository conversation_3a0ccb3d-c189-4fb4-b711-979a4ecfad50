<template>
    <span>当前井号：</span>
    <el-button type="success" :disabled="!wellInfo.id" plain round size="small" @click="visible=!visible" icon="el-icon-help-filled">{{
        wellInfo.name
    }}</el-button>

    <el-dialog
        v-model="visible"
        :title="wellInfo.name + ' 井信息详情'"
        width="60%"
        destroy-on-close
        append-to-body
    >
        <el-descriptions :column="2" border>
            <el-descriptions-item label="井号">{{ wellInfo.id }}</el-descriptions-item>
            <el-descriptions-item label="井名">{{ wellInfo.name }}</el-descriptions-item>
            <el-descriptions-item label="状态">
                <el-tag type="success">{{ getStatusType(wellInfo.status) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="所在区域">{{ wellInfo.location }}</el-descriptions-item>
            <el-descriptions-item label="层位数">{{ wellInfo.level }}层</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ wellInfo.create_at }}</el-descriptions-item>
            <el-descriptions-item label="注水量" :span="2">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-statistic title="日注水量" value="234.5" suffix="m³/d" />
                    </el-col>
                    <el-col :span="8">
                        <el-statistic title="月累计注水量" value="6789.2" suffix="m³" />
                    </el-col>
                    <el-col :span="8">
                        <el-statistic title="年累计注水量" value="45678.9" suffix="m³" />
                    </el-col>
                </el-row>
            </el-descriptions-item>
            <el-descriptions-item label="运行参数" :span="2">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-statistic title="注水压力" value="12.5" suffix="MPa" />
                    </el-col>
                    <el-col :span="8">
                        <el-statistic title="油压" value="8.3" suffix="MPa" />
                    </el-col>
                    <el-col :span="8">
                        <el-statistic title="套压" value="5.2" suffix="MPa" />
                    </el-col>
                </el-row>
            </el-descriptions-item>
        </el-descriptions>
    </el-dialog>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
    name: 'wellInfo',
    props: {
        wellInfo: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            visible: false
        }
    },
    methods: {
        getStatusType(status: string) {
            const types = {
                0: '停用',
                1: '正常',
                2: '异常',
                3: '维修'
            };
            return types[status] || '正常';
        }
    }
});
</script>

<style lang="scss" scoped>
.el-descriptions {
    padding: 20px;
}
</style>
