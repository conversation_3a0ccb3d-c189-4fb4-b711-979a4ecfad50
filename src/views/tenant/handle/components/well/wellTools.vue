<template>
    <section class="tools-container">
        <header class="tools-header">
            当前层位：{{layerInfo.layer}}
        </header>
        <main class="tools-main">
            <el-form :model="form" label-width="auto" label-position="left" size="small">
                <!-- 动态数据项 -->
                <el-form-item v-for="item in dynamicFormItems" :key="item.label" :label="item.label">
                    <el-input-number
                        v-if="item.type === 'number'"
                        v-model="form[item.field]"
                        :step="0.1"
                        :min="0.0"
                        :max="50.0"
                        controls-position="right"
                        style="width:100%"
                    >
                        <template #suffix>
                            <span>{{item.unit}}</span>
                        </template>
                    </el-input-number>
                    <el-input
                        v-else
                        v-model="form[item.field]"
                        :disabled="item.disabled"
                    />
                </el-form-item>
            </el-form>
        </main>
        <footer class="tools-footer">
            <el-button size="small" type="primary" @click="onSubmit">停止实测</el-button>
            <el-button size="small">停止电机</el-button>
        </footer>
    </section>
</template>

<script>
export default {
    name: "wellTools",
    props: {
        // 当前油井信息
        wellInfo: {
            type: Object,
            required: true,
            default: () => ({
                id: 0,
                name: '',
                level: 0
            })
        },
        // 当前油井的当前分层信息
        layerInfo: {
            type: Object,
            required: true,
            default: () => ({
                layer: 0,
                name: ''
            })
        },
    },
    data() {
        return {
            form: {
                flow: 0,
                injectPressure: 0,
                layerPressure: 0,
                nozzleRings: '2.0 (16%)',
                temperature: '36.5',
                humidity: '29',
                voltage: '59.0',
                motorCurrent: '-',
                deviceModel: 'CZT-BC',
                deviceNumber: '1603002',
                productionDate: '2016-03-20',
                injectionFlow: '20',
                workStatus: '实时测量',
                adjustmentStatus: '',
                subAdjustCount: '',
                fineAdjustCount: '',
                busAmpere: '78',
                busVoltage: '62.0',
                groundMeterNumber: '10001',
                autoSave: '2分钟'
            },
            dynamicFormItems: [
                { label: '流量', field: 'flow', type: 'number', unit: 'm³/d' },
                { label: '注入压力', field: 'injectPressure', type: 'number', unit: 'MPa' },
                { label: '地层压力', field: 'layerPressure', type: 'number', unit: 'MPa' },
                { label: '调整', field: 'adjustment', value: '未调节' },
                { label: '水嘴圈数', field: 'nozzleRings' },
                { label: '温度', field: 'temperature', disabled: true },
                { label: '湿度', field: 'humidity', disabled: true },
                { label: '设备电压', field: 'voltage', disabled: true },
                { label: '电机电流', field: 'motorCurrent', disabled: true },
                { label: '仪器型号', field: 'deviceModel', disabled: true },
                { label: '仪器编号', field: 'deviceNumber', disabled: true },
                { label: '出厂日期', field: 'productionDate', disabled: true },
                { label: '配注流量', field: 'injectionFlow', disabled: true },
                { label: '工作状态', field: 'workStatus' },
                { label: '调节状态', field: 'adjustmentStatus' },
                { label: '子调次数', field: 'subAdjustCount' },
                { label: '微调次数', field: 'fineAdjustCount' },
                { label: '总线电流', field: 'busAmpere', disabled: true },
                { label: '总线电压', field: 'busVoltage', disabled: true },
                { label: '地面表编号', field: 'groundMeterNumber', disabled: true },
                { label: '自动保存', field: 'autoSave', disabled: true }
            ]
        };
    },
    computed: {
        adjustmentStatus() {
            return `${this.layerInfo.layer}层（未调节）`;
        }
    },
    methods: {
        onSubmit() {
            console.log('onSubmit!')
        }
    }
};
</script>

<style scoped lang="scss">
.tools-container {
    height: calc(100vh - 70px - 30px - 36px); // 减去 layout-header(80px) 和 layout-footer(30px) 的高度
    display: flex;
    flex-direction: column;
    overflow: hidden; // 防止内容溢出
}

.tools-header,
.tools-footer {
    flex: 0 0 auto; // 固定高度，不参与伸缩
    height: 36px;
    line-height: 33px;
    padding: 0 15px;
}

.tools-header {
    border-bottom: 1px solid #eeeeee;
}

.tools-footer {
    border-top: 1px solid #eeeeee;
}

.tools-main {
    flex: 1; // 自动填充剩余空间
    padding: 15px;
    overflow-y: auto;
    min-height: 0; // 关键：防止flex项在overflow时的异常
}

html[data-theme='dark'] {
    .tools-header, .tools-footer {
        border-color: var(--el-border-color);
    }
}
</style>
