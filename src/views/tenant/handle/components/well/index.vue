<template>
    <el-container class="well-layout" v-loading="loading">
        <el-header>
            <wellHeader :wellInfo="wellInfo" :pageInfo="pageInfo" />
        </el-header>
        <el-container>
            <wellMain :wellInfo="wellInfo" :pageInfo="pageInfo" :loading="loading" />
        </el-container>
    </el-container>
</template>

<script>
import wellHeader from './wellHeader.vue';
import wellMain from './wellMain.vue';
import { smartFetchDeviceInfo, calculatePageTotal } from '@/utils/deviceUtils';

export default {
    name: 'wellLayout',
    components: {
        wellHeader,
        wellMain
    },
    data() {
        return {
            // 显示loading
            loading: true,
            wellId: null,
            wellInfo: {
                id: 0,
                name: '',
                level: 0
            },
            pageInfo: {
                currentPage: 1,
                pageShow: 4,
                pageTotal: 1
            }
        };
    },
    watch: {
        '$route.params.id': {
            handler(newId) {
                if (newId && this.wellId !== newId) {
                    this.wellId = newId;
                    this.fetchWellInfo();
                }
            },
            immediate: true
        },
        // 监听路由参数变化
        '$route.query.page': {
            handler(newPage) {
                if (newPage) {
                    this.pageInfo.currentPage = parseInt(newPage) || 1;
                }
            },
            immediate: true
        }
    },
    methods: {
        /**
         * 获取油井数据
         * @returns {Promise<void>}
         */
        async fetchWellInfo() {
            this.loading = true;

            try {
                // 智能获取设备信息（优先使用父组件数据）
                const deviceInfo = await smartFetchDeviceInfo(this, this.wellId, {
                    silent: false
                });

                if (deviceInfo) {
                    this.wellInfo = deviceInfo;
                    // 计算分页信息
                    this.pageInfo.pageTotal = calculatePageTotal(deviceInfo.level, this.pageInfo.pageShow);
                }
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.el-header {
    height: 36px;
    padding: 0 15px;
}
</style>
