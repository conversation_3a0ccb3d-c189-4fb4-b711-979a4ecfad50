<template>
    <el-dialog
        v-model="dialogVisible"
        title="设备分配"
        width="700px"
        :draggable="true"
        :destroy-on-close="true"
        :close-on-click-modal="false"
        @closed="dialogClosed"
        :modal-append-to-body="true"
        :append-to-body="true"
    >
        <div class="dialog-content">
            <el-container class="device-selection-container">
                <div class="header-container">
                    <div class="role-info">
                        <el-form-item label="角色名称：">
                            <span>{{ currentRole.name }}</span>
                        </el-form-item>
                    </div>
                    <div class="search-bar">
                        <div class="right-panel-search">
                            <el-input
                                v-model="searchParams.keyword"
                                placeholder="设备名称/设备标识"
                                @input="handleSearchDebounced"
                                @clear="handleSearchDebounced"
                                clearable
                            >
                            </el-input>
                            <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="handleSearchDebounced"
                                >搜索</el-button
                            >
                        </div>
                    </div>
                </div>
                <el-main class="table-container">
                    <scTable
                        ref="deviceDataTable"
                        :params="searchParams"
                        :apiObj="deviceDataObj"
                        remoteFilter
                        remoteSort
                        stripe
                        row-key="id"
                        @selection-change="handleSelectionChange"
                        @row-click="handleRowClick"
                        @load="handleTableLoad"
                        class="custom-list-table"
                    >
                        <el-table-column type="selection" width="50" :reserve-selection="true" />
                        <el-table-column prop="name" label="设备名称" show-overflow-tooltip />
                        <el-table-column prop="code" label="设备标识" show-overflow-tooltip />
                        <el-table-column prop="status" label="设备状态" show-overflow-tooltip>
                            <template #default="scope">
                                <el-tag :type="scope.row.status === 0 ? 'warning' : scope.row.status === 1 ? 'success' : scope.row.status === 2 ? 'danger' : 'info'">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注" show-overflow-tooltip />
                    </scTable>
                </el-main>
            </el-container>
        </div>
        <template #footer>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
        </template>
    </el-dialog>
</template>

<script>
import tableConfig from '@/config/table.js';
import { debounce } from '@/utils/throttleDebounce';

export default {
    name: 'tenant.role.permission',
    data() {
        return {
            statusOptions: [
                { text: '停用', value: 0 },
                { text: '正常', value: 1 },
                { text: '异常', value: 2 },
                { text: '维修', value: 3 }
            ],
            getStatusText(status) {
                const option = this.statusOptions.find(item => item.value === status);
                return option ? option.text : '';
            },
            dialogVisible: false,
            loading: true,
            deviceDataObj: this.$API.tenant.device.list,
            selection: [], // 角色当前选中的所有设备ID
            initialSelection: [], // 初始加载时角色已有的设备ID
            rowSelectFlag: false, // 禁止toggleRowSelection默认触发selection-change事件
            currentPageIds: [], // 当前页面所有设备的ID
            currentRole: {
                id: '',
                name: ''
            },
            searchParams: {
                keyword: '',
                role_id: '' // 添加角色ID参数
            }
        };
    },

    mounted() {
        this.$nextTick(() => this.setupTableMethodsOverride());
    },

    methods: {
        /**
         * 重置所有状态
         * 将所有状态恢复到初始空值，用于对话框关闭或需要清空状态的场景
         */
        resetState() {
            // 完全重置搜索参数
            this.searchParams = {
                keyword: '',
                role_id: ''
            };

            // 重置选中状态和页面状态
            this.selection = [];
            this.initialSelection = [];
            this.currentPageIds = [];
        },
        /**
         * 初始化角色的设备数据
         * @param {Object} roleData - 包含角色ID和已有设备的数据
         */
        initRoleDevices(roleData) {
            this.currentRole.id = roleData.id;
            this.currentRole.name = roleData.name;
            this.searchParams.role_id = roleData.id;

            // 储存初始设备IDs
            this.initialSelection = roleData.device_ids ? roleData.device_ids.map(id => Number(id)) : [];
            this.selection = [...this.initialSelection];
        },
        /**
         * 显示对话框并加载数据
         */
        async show(row) {
            this.dialogVisible = true;

            // 初始化角色数据
            this.initRoleDevices(row);

            await this.$nextTick();
            this.setupTableMethodsOverride();

            this.loading = false;
        },
        /**
         * 表格数据加载完成
         */
        handleTableLoad(data) {
            // 保存当前页面ID，用于选择处理
            this.currentPageIds = data.map(row => Number(row.id));

            // 设置选中状态
            this.$nextTick(() => {
                this.rowSelectFlag = true;

                const table = this.$refs.deviceDataTable.$refs.scTable;
                table.clearSelection();

                // 根据selection选中对应行
                data.forEach(row => {
                    if (this.selection.includes(Number(row.id))) {
                        table.toggleRowSelection(row, true);
                    }
                });

                // 恢复标志位
                this.$nextTick(() => {
                    this.rowSelectFlag = false;
                });
            });
        },
        /**
         * 设备选择状态变更
         */
        handleSelectionChange(selection) {
            if (this.rowSelectFlag) return;

            // 获取当前页面选中ID
            const currentSelectedIds = selection.map(row => Number(row.id));

            // 更新选中状态：保留其他页的选择，更新当前页的选择
            this.selection = [
                ...this.selection.filter(id => !this.currentPageIds.includes(id)),
                ...currentSelectedIds
            ];
        },
        /**
         * 搜索防抖方法
         */
        handleSearchDebounced: debounce(function() {
            this.loadTableData();
        }, 300),
        /**
         * 点击选择数据
         */
        handleRowClick(row) {
            this.$refs.deviceDataTable.$refs.scTable.toggleRowSelection(row);
        },
        /**
         * 加载表格数据
         */
        loadTableData() {
            if (!this.$refs.deviceDataTable) return;
            this.$refs.deviceDataTable.upData(this.searchParams);
        },
        /**
         * 设置表格方法覆盖
         */
        setupTableMethodsOverride() {
            const table = this.$refs.deviceDataTable;
            if (!table || table._methodsOverridden) return;

            table._methodsOverridden = true;

            // 创建保护选中状态的方法包装器
            const protectSelection = (target, methodName, originalMethod) => {
                return function(...args) {
                    // 保存原始选中状态
                    const originalSelectedIds = [...this.selection];

                    // 调用原始方法
                    const result = originalMethod.apply(target, args);

                    // 恢复选中状态
                    this.$nextTick(() => {
                        this.selection = originalSelectedIds;
                    });

                    return result;
                }.bind(this);
            };

            // 劫持clearSelection、refresh和upData方法
            if (table.$refs.scTable) {
                table.$refs.scTable.clearSelection = protectSelection(
                    table.$refs.scTable,
                    'clearSelection',
                    table.$refs.scTable.clearSelection
                );
            }

            table.refresh = protectSelection(table, 'refresh', table.refresh);
            table.upData = protectSelection(table, 'upData', table.upData);
        },
        /**
         * 提交表单
         * @returns {Promise<void>}
         */
        async submitForm() {
            if (!this.currentRole.id) {
                this.$message.warning('角色信息不完整');
                return;
            }

            this.loading = true;
            try {
                const res = await this.$API.tenant.role.assignDevices.post({
                    id: this.currentRole.id,
                    device_ids: this.selection
                });
                if (res.code === tableConfig.successCode) {
                    this.$message.success('设备分配成功');

                    // 通知父组件更新数据，传递最新的设备ID列表
                    this.$emit('deviceUpdated', {
                        roleId: this.currentRole.id,
                        device_ids: [...this.selection]
                    });

                    this.dialogVisible = false;
                } else {
                    this.$message.error(res.message || '设备分配失败');
                }
            } catch (error) {
                console.error('设备分配错误:', error);
                this.$message.error('操作失败: ' + (error.message || '未知错误'));
            } finally {
                this.loading = false;
            }
        },
        /**
         * 每次对话框关闭时重置状态
         */
        dialogClosed() {
            // 重置所有状态
            this.resetState();

            // 发送关闭事件通知父组件
            this.$emit('deviceDialogClosedEmit');
        }
    }
};
</script>

<style lang="scss" scoped>
.dialog-content,
.device-selection-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.role-info {
    .el-form-item {
        margin-bottom: 0;
    }
}

.search-bar {
    display: flex;
    justify-content: flex-end;
}

.right-panel-search {
    display: flex;
    align-items: center;
}

.table-container {
    padding: 0;
    border: 1px solid #efefef;
    height: 300px;
}

.selected-row {
    background-color: var(--el-color-primary-light-9);
}

.el-table {
    cursor: pointer;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
}

.empty-data {
    padding: 30px 0;
    text-align: center;
    color: #909399;

    .empty-tip {
        font-size: 12px;
        margin-top: 8px;
        color: #c0c4cc;
    }
}
</style>
