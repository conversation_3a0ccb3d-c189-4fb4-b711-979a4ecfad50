<template>
    <selection-dialog
        v-model="dialogVisible"
        title="角色设备授权"
        :loading="loading"
        @submit="submitForm"
        @closed="dialogClosed"
    >
        <device-selector
            ref="deviceSelector"
            :entityInfo="{ title: '角色', content: currentRole.name }"
            :search-params="searchParams"
            :initial-selection="initialSelection"
            @selection-change="handleSelectionChange"
        />
    </selection-dialog>
</template>

<script>
import tableConfig from '@/config/table.js';
import DeviceSelector from '../common/DeviceSelector.vue';
import SelectionDialog from '../common/SelectionDialog.vue';

export default {
    name: 'tenant.role.permission',
    components: {
        DeviceSelector,
        SelectionDialog
    },
    data() {
        return {
            dialogVisible: false,
            loading: false,
            selection: [], // 当前选中的设备ID数组
            initialSelection: [], // 初始选中的设备ID数组
            currentRole: {
                id: '',
                name: ''
            },
            searchParams: {
                keyword: '',
                role_id: '' // 添加角色ID参数
            }
        };
    },
    methods: {
        /**
         * 重置状态
         */
        resetState() {
            this.searchParams = {
                keyword: '',
                role_id: ''
            };
            this.selection = [];
            this.initialSelection = [];
            this.currentRole = {
                id: '',
                name: ''
            };
        },

        /**
         * 显示对话框并加载数据
         * @param {Object} roleData 角色数据
         */
        async show(roleData) {
            if (!roleData || !roleData.id) {
                this.$message.error('角色数据不完整');
                return this;
            }

            // 设置角色信息
            this.currentRole = {
                id: roleData.id,
                name: roleData.name
            };
            this.searchParams.role_id = roleData.id;

            // 获取已分配的设备ID
            try {
                const res = await this.$API.tenant.role.getDeviceIds.post({
                    id: roleData.id
                });

                if (res.code === tableConfig.successCode && res.data) {
                    this.initialSelection = res.data.device_ids ? res.data.device_ids.map(id => Number(id)) : [];
                    this.selection = [...this.initialSelection];
                }
            } catch (error) {
                console.error('获取角色设备错误:', error);
                this.$message.error('获取已分配设备失败: ' + (error.message || '未知错误'));
            }

            // 显示对话框
            this.dialogVisible = true;

            return this;
        },

        /**
         * 处理选择变更
         * @param {Array} selection 选中的设备ID数组
         */
        handleSelectionChange(selection) {
            this.selection = selection;
        },

        /**
         * 提交表单
         */
        async submitForm() {
            if (!this.currentRole.id) {
                this.$message.warning('角色信息不完整');
                return;
            }

            this.loading = true;
            try {
                const res = await this.$API.tenant.role.assignDevices.post({
                    id: this.currentRole.id,
                    device_ids: this.selection
                });

                if (res.code === tableConfig.successCode) {
                    this.$message.success('设备授权成功');

                    // 通知父组件更新数据
                    this.$emit('deviceUpdated', {
                        roleId: this.currentRole.id,
                        device_ids: [...this.selection]
                    });

                    this.dialogVisible = false;
                } else {
                    this.$message.error(res.message || '设备授权失败');
                }
            } catch (error) {
                console.error('设备授权错误:', error);
                this.$message.error('操作失败: ' + (error.message || '未知错误'));
            } finally {
                this.loading = false;
            }
        },

        /**
         * 对话框关闭事件
         */
        dialogClosed() {
            // 重置所有状态
            this.resetState();
            // 发送关闭事件通知父组件
            this.$emit('deviceDialogClosedEmit');
        }
    }
};
</script>

<style lang="scss" scoped>
/* 保留原有的样式，如果需要 */
</style>
