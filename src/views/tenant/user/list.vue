<template>
    <el-container style="overflow: hidden">
        <el-container>
            <el-header>
                <div class="left-panel">
                    <el-button
                        v-auths="['tenant.user.add']"
                        icon="sc-icon-user-add"
                        type="primary"
                        @click="user_add"
                        color="#1C409A"
                        >添加用户</el-button
                    >
                    <!-- <el-button
                        v-auths="['tenant.user.delete']"
                        :disabled="selection.length === 0"
                        icon="el-icon-delete"
                        plain
                        type="danger"
                        @click="batch_delete"
                        >删除</el-button
                    > -->
                </div>
                <div class="right-panel">
                    <div class="right-panel-search">
                        <el-input
                            style="min-width: 180px"
                            v-model="searchObj.keyword"
                            clearable
                            placeholder="用户名/姓名/手机号/邮箱号"
                            @keyup.enter="searchHandle"
                            @clear="searchHandle"
                        ></el-input>
                        <el-button
                            icon="el-icon-search"
                            type="primary"
                            color="#1C409A"
                            @click="searchHandle"
                        ></el-button>
                    </div>
                </div>
            </el-header>
            <el-main class="p0">
                <scTable
                    ref="userDataTable"
                    :apiObj="userDataObj"
                    remoteFilter
                    remoteSort
                    stripe
                    @selection-change="selectionChange"
                    class="custom-list-table"
                >
                    <el-table-column type="selection" width="50"></el-table-column>
                    <el-table-column label="UID" prop="id" sortable="custom" width="80" fixed></el-table-column>
                    <el-table-column
                        label="登录账号"
                        prop="username"
                        sortable="custom"
                        width="150"
                        fixed
                    ></el-table-column>
                    <el-table-column
                        label="账号名称"
                        prop="realname"
                        show-overflow-tooltip
                        sortable="custom"
                        width="150"
                    ></el-table-column>
                    <el-table-column
                        label="手机号"
                        prop="mobile"
                        show-overflow-tooltip
                        sortable="custom"
                        width="150"
                    ></el-table-column>
                    <el-table-column
                        label="邮箱号"
                        prop="email"
                        show-overflow-tooltip
                        sortable="custom"
                        width="200"
                    ></el-table-column>
                    <el-table-column
                        label="性别"
                        prop="gender"
                        width="100"
                        align="center"
                    >
                        <template #default="scope">
                            <el-tag v-if="scope.row.gender === 1" type="success">男</el-tag>
                            <el-tag v-else-if="scope.row.gender === 2" type="info">女</el-tag>
                            <el-tag v-else type="warning">未知</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="生日"
                        prop="birthday"
                        show-overflow-tooltip
                        width="120"
                    ></el-table-column>
                    <el-table-column
                        label="创始人"
                        prop="founder"
                        width="100"
                        align="center"
                    >
                        <template #default="scope">
                            <el-tag v-if="scope.row.founder === 1" type="success">是</el-tag>
                            <el-tag v-else type="info">否</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        :filters="[
                            { text: '已冻结', value: '0' },
                            { text: '正常', value: '1' }
                        ]"
                        column-key="filter[status]"
                        align="center"
                        label="用户状态"
                        prop="status"
                        width="100"
                    >
                        <template #default="scope">
                            <el-switch
                                v-auths="['tenant.user.status']"
                                size="small"
                                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #bfbfbf"
                                v-model="scope.row.status"
                                :loading="scope.row.loading || false"
                                :active-value="1"
                                :inactive-value="0"
                                @change="switchUserStatus(scope.row)"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="创建时间"
                        prop="create_at"
                        show-overflow-tooltip
                        sortable="custom"
                        width="170"
                    ></el-table-column>
                    <el-table-column align="center" fixed="right" label="操作" width="220">
                        <template #default="scope">
                            <el-button
                                v-auths="['tenant.user.edit']"
                                size="small"
                                type="primary"
                                @click="user_edit(scope.row, scope.$index)"
                                color="#1C409A"
                                >编辑</el-button
                            >
                            <el-button
                                v-auths="['tenant.user.role']"
                                size="small"
                                type="primary"
                                @click="user_role(scope.row)"
                                color="#32aa66"
                                >角色分配</el-button
                            >
                            <el-button
                                v-auths="['tenant.user.device']"
                                size="small"
                                type="primary"
                                @click="user_device(scope.row)"
                                color="#944df7"
                            >
                                授权
                            </el-button>
                            <el-popconfirm title="确定删除该用户吗？" @confirm="user_delete(scope.row, scope.$index)">
                                <template #reference>
                                    <el-button
                                        v-auths="['tenant.user.delete']"
                                        size="small"
                                        type="danger"
                                        >删除</el-button
                                    >
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </scTable>
            </el-main>
        </el-container>
    </el-container>

    <!--添加编辑对话框-->
    <save-dialog
        v-if="dialog.save"
        ref="saveDialog"
        @saveDialogClosedEmit="saveDialogClosedHandle"
        @saveSuccessEmit="saveSuccessHandle"
    ></save-dialog>

    <!--角色分配对话框-->
    <role-dialog
        v-show="dialog.role"
        ref="roleDialog"
        @roleDialogClosedEmit="roleDialogClosedHandle"
        @roleUpdated="handleRoleUpdated"
    ></role-dialog>

    <!--设备授权对话框-->
    <device-dialog
        v-show="dialog.device"
        ref="deviceDialog"
        @deviceDialogClosedEmit="deviceDialogClosedHandle"
        @deviceUpdated="handleDeviceUpdated"
    ></device-dialog>
</template>

<script>
import saveDialog from './save';
import roleDialog from './role';  // 导入角色分配组件
import deviceDialog from './device';  // 导入设备授权组件
import tableConfig from '@/config/table.js';

export default {
    name: 'tenant.user.list',
    components: {
        saveDialog,
        roleDialog,
        deviceDialog
    },
    data() {
        return {
            dialog: {
                save: false,
                role: false,  // 添加角色对话框控制
                device: false  // 添加设备授权对话框控制
            },
            userDataObj: this.$API.tenant.user.list,
            selection: [],
            searchObj: {
                keyword: null
            }
        };
    },
    methods: {
        /**
         * 用户添加
         */
        user_add() {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show();
            });
        },
        /**
         * 用户编辑
         * @param row
         */
        user_edit(row) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show('edit').setFormData(row, false);
            });
        },
        /**
         * 单个用户删除
         * @param row
         * @param index
         * @returns {Promise<void>}
         */
        async user_delete(row, index) {
            const reqData = { id: row.id };
            const res = await this.$API.tenant.user.delete.post(reqData);
            if (res.code === tableConfig.successCode) {
                this.$refs.userDataTable.tableData.splice(index, 1);
                this.$message.success('删除成功');
            } else {
                await this.$alert(res.message, '提示', { type: 'error' });
            }
        },
        /**
         * 角色分配
         */
        user_role(row) {
            this.dialog.role = true;
            this.$nextTick(() => {
                this.$refs.roleDialog.show(row);
            });
        },
        /**
         * 设备授权
         */
        user_device(row) {
            this.dialog.device = true;
            this.$nextTick(() => {
                this.$refs.deviceDialog.show(row);
            });
        },
        /**
         * 角色对话框关闭事件
         */
        roleDialogClosedHandle() {
            this.dialog.role = false;
        },
        /**
         * 设备授权对话框关闭事件
         */
        deviceDialogClosedHandle() {
            this.dialog.device = false;
        },
        /**
         * 表格选择后回调事件
         * @param selection
         */
        selectionChange(selection) {
            this.selection = selection;
        },
        /**
         * 搜索
         */
        searchHandle() {
            this.$refs.userDataTable.refresh();
        },
        /**
         * 用户状态切换
         * @param row
         */
        switchUserStatus(row) {
            row.loading = true;
            const api = row.status === 1 ? this.$API.tenant.user.enable : this.$API.tenant.user.disable;
            api.post({ id: row.id })
                .then(res => {
                    if (res.code === tableConfig.successCode) {
                        this.$message.success('操作成功');
                    } else {
                        row.status = row.status === 1 ? 0 : 1;
                        this.$message.error(res.message);
                    }
                })
                .catch(() => {
                    row.status = row.status === 1 ? 0 : 1;
                })
                .finally(() => {
                    row.loading = false;
                });
        },
        /**
         * 保存对话框关闭事件
         */
        saveDialogClosedHandle() {
            this.dialog.save = false;
        },
        /**
         * 保存成功事件
         */
        saveSuccessHandle() {
            this.$refs.userDataTable.refresh();
        },
        /**
         * 角色分配成功后的处理事件
         */
        handleRoleUpdated(data) {
            // 查找并更新用户的角色数据
            const tableData = this.$refs.userDataTable.tableData;
            const userIndex = tableData.findIndex(user => user.id === data.userId);

            if (userIndex !== -1) {
                // 直接更新用户角色ID数组
                tableData[userIndex].role_ids = data.role_ids;
                this.$message.success('用户角色已更新');
            }
        },
        /**
         * 设备授权成功后的处理事件
         */
        handleDeviceUpdated(data) {
            // 查找并更新用户的设备数据
            const tableData = this.$refs.userDataTable.tableData;
            const userIndex = tableData.findIndex(user => user.id === data.userId);

            if (userIndex !== -1) {
                // 直接更新用户设备ID数组
                tableData[userIndex].device_ids = data.device_ids;
                this.$message.success('用户设备授权已更新');
            }
        }
    }
};
</script>
