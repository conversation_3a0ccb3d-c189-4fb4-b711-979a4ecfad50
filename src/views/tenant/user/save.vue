<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="800px"
        :destroy-on-close="true"
        :draggable="true"
        :close-on-click-modal="false"
        @closed="dialogClosed"
    >
        <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="100px"
            label-position="right"
        >
            <el-form-item label="登录账号" prop="username">
                <el-input
                    v-model="formData.username"
                    placeholder="请输入登录账号"
                    :disabled="dialogType === 'edit'"
                ></el-input>
            </el-form-item>
            <el-form-item label="用户姓名" prop="realname">
                <el-input v-model="formData.realname" placeholder="请输入用户姓名"></el-input>
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
                <el-input v-model="formData.mobile" placeholder="请输入手机号码"></el-input>
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
                <el-input v-model="formData.email" placeholder="请输入邮箱"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
                <el-input
                    v-model="formData.password"
                    type="password"
                    placeholder="请输入登录密码"
                    show-password
                    :disabled="false"
                ></el-input>
            </el-form-item>
            <el-form-item label="生日" prop="birthday">
                <el-date-picker
                    v-model="formData.birthday"
                    type="date"
                    placeholder="选择生日"
                    value-format="YYYY-MM-DD"
                ></el-date-picker>
            </el-form-item>
            <el-form-item label="头像" prop="avatar">
                <el-input v-model="formData.avatar" placeholder="请输入头像URL"></el-input>
            </el-form-item>
            <el-form-item label="性别" prop="gender">
                <el-select v-model="formData.gender" placeholder="请选择性别">
                    <el-option label="未知" :value="0"></el-option>
                    <el-option label="男" :value="1"></el-option>
                    <el-option label="女" :value="2"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="description">
                <el-input v-model="formData.description" type="textarea" placeholder="请输入备注信息"></el-input>
            </el-form-item>
            <el-form-item label="用户状态" prop="status">
                <el-switch
                    v-model="formData.status"
                    :active-value="1"
                    :inactive-value="0"
                ></el-switch>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
        </template>
    </el-dialog>
</template>

<script>
import { reactive, ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import tableConfig from '@/config/table.js';
import { getCurrentInstance } from 'vue';

export default {
    name: 'tenant.user.save',
    emits: ['saveDialogClosedEmit', 'saveSuccessEmit'],
    setup(props, { emit }) {
        const { proxy } = getCurrentInstance();
        const formRef = ref(null);
        const dialogVisible = ref(false);
        const dialogType = ref('');
        const dialogTitle = ref('');
        const roleDialogVisible = ref(false);
        const roleList = ref([]);
        const selectedRoles = ref([]);
        const formData = reactive({
            id: '',
            username: '',
            realname: '',
            mobile: '',
            email: '',
            avatar: '',
            birthday: '',
            password: '',
            status: 1,
            gender: 0,
            description: '',
        });

        const formRules = computed(() => ({
            username: [
                { required: true, message: '请输入登录账号', trigger: 'blur' },
                { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
            ],
            realname: [
                { required: true, message: '请输入用户姓名', trigger: 'blur' },
                { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
            ],
            mobile: [
                { required: true, message: '请输入手机号码', trigger: 'blur' },
                { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
            ],
            email: [
                { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
            ],
            gender: [
                { required: true, message: '请选择性别', trigger: 'change' }
            ],
            description: [
                { max: 255, message: '备注长度不能超过255个字符', trigger: 'blur' }
            ],
            password: [
                { required: dialogType.value === 'add', message: '请输入登录密码', trigger: 'blur' }
            ]
        }));

        const show = (type = 'add', data = null) => {
            dialogType.value = type;
            dialogVisible.value = true;
            dialogTitle.value = type === 'add' ? '添加用户' : '编辑用户';

            // 重置表单
            Object.assign(formData, {
                id: '',
                username: '',
                realname: '',
                mobile: '',
                status: 1,
                gender: 0,
                description: '',
                password: ''
            });

            // 如果是编辑,则覆盖数据
            if(type === 'edit' && data) {
                Object.assign(formData, data);
                formData.password = ''; // 清空密码字段
            }

            return {
                setFormData: (data, isBatch = false) => {
                    isBatch ? (formData.ids = data) : Object.assign(formData, data);
                }
            };
        };

        const submitForm = () => {
            formRef.value.validate(valid => {
                if (valid) {
                    const reqData = { ...formData };
                    // 如果有密码则加密,没有则删除该字段
                    reqData.password ? (reqData.password = proxy.$TOOL.crypto.MD5(reqData.password)) : delete reqData.password;

                    proxy.$API.tenant.user[dialogType.value === 'add' ? 'add' : 'edit'].post(reqData)
                        .then(res => {
                            if (res.code === tableConfig.successCode) {
                                ElMessage.success(res.message);
                                dialogVisible.value = false;
                                emit('saveSuccessEmit');
                            } else {
                                ElMessage.error(res.message);
                            }
                        })
                        .catch(() => ElMessage.error('操作失败'));
                }
            });
        };

        const showRoleDialog = () => {
            roleDialogVisible.value = true;
            getRoleList();
            getUserRoles();
        };

        const getRoleList = async () => {
            const res = await proxy.$API.tenant.role.list.get();
            if (res.code === tableConfig.successCode) {
                roleList.value = res.data;
            }
        };

        const getUserRoles = async () => {
            if (!formData.id) return;
            const res = await proxy.$API.tenant.user.roles.get({ id: formData.id });
            if (res.code === tableConfig.successCode) {
                selectedRoles.value = res.data;
            }
        };

        const saveUserRoles = async () => {
            const res = await proxy.$API.tenant.user.assignRoles.post({
                id: formData.id,
                roleIds: selectedRoles.value
            });
            if (res.code === tableConfig.successCode) {
                ElMessage.success('分配角色成功');
                roleDialogVisible.value = false;
            } else {
                ElMessage.error(res.message);
            }
        };

        const dialogClosed = () => {
            emit('saveDialogClosedEmit');
        };

        return {
            formRef,
            dialogVisible,
            dialogType,
            dialogTitle,
            formData,
            formRules,
            roleDialogVisible,
            roleList,
            selectedRoles,
            show,
            submitForm,
            dialogClosed,
            showRoleDialog,
            saveUserRoles
        };
    }
};
</script>
