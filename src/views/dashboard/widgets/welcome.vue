<template>
    <div class="welcome-tip">
        <el-alert :closable="false" :show-icon="false" class="welcome-tip-wrapper" type="success">
            <template #title>
                <el-icon class="smile-icon"><sc-icon-smile /></el-icon>
                <div :class="{ monitor: isMonitor, 'welcome-tip--content': true }">
                    <h4>
                        <span>{{ userName }}</span>
                        <!--时间段问候语提示组件-->
                        <greeting></greeting>
                    </h4>
                    <hr />
                    <!--每日一句组件-->
                    <sentence></sentence>
                </div>
            </template>
        </el-alert>
    </div>
</template>

<script>
import greeting from '@/views/dashboard/widgets/components/greeting';
import sentence from '@/views/dashboard/widgets/components/sentence';

export default {
    name: 'welcome',
    components: {
        greeting,
        sentence
    },
    props: {
        // 接收父组件传来的数据
        isMonitor: {
            type: Boolean,
            default: () => false
        },
        // 接收父组件传来的数据
        userInfoObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            // 被问候人称呼
            userName: ''
        };
    },
    mounted() {
        if (!this.isMonitor) {
            const username = this.userInfoObj.realname;
            this.userName = username ? username + '，' : '';
        }

        this.$emit('on-mounted');
    },
    methods: {}
};
</script>

<style scoped lang="scss">
.welcome-tip {
    margin: 0 0 10px 0;
}

.welcome-tip-wrapper {
    background-color: color-mix(in srgb, var(--el-color-primary) 15%, white 50%);
    border: 1px solid color-mix(in srgb, var(--el-color-primary) 20%, white 50%);
    border-radius: 0.25rem;
    color: color-mix(in srgb, var(--el-color-primary) 85%, var(--el-color-primary) 10%);
    display: table;
    line-height: 2;
    margin-bottom: 20px;
    padding: 0.75rem 1.25rem;
    position: relative;

    :deep(.el-alert__title) {
        display: table;
        line-height: inherit;
        width: 100%;
    }

    .smile-icon {
        display: table-cell;
        font-size: 70px;
        opacity: 0.6;

        svg {
            position: relative;
            top: 6px;
        }
    }

    .welcome-tip--content {
        display: table-cell;
        float: none;
        margin: 0;
        min-height: 0;
        padding-left: 15px;
        vertical-align: middle;

        h4 {
            font-size: 16px;
            font-weight: 600;
            line-height: 1.2;
            margin: 10px 0;
        }

        span {
            font-size: 14px;
            line-height: 1.2;
        }

        hr {
            border: 0;
            // 分割线使用与边框相同的颜色
            border-top: 1px solid color-mix(in srgb, var(--el-color-primary) 20%, white 50%);
            box-sizing: content-box;
            height: 0;
            margin: 10px 0;
            overflow: visible;
            width: 100%;
        }
    }
}
</style>
