<template>
    <div @click="getSentences" v-if="theme == 'default'">
        <span>『 {{ sentences.statement }} 』—— 《{{ sentences.from }}》</span>
    </div>
    <div @click="getSentences" v-if="theme == 'classic'">
        <span class="--main">「 {{ sentences.statement }} 」</span>
        <span class="--foot">—— 《{{ sentences.from }}》</span>
    </div>
</template>

<script>
/**
 * 每日一句组件
 */
export default {
    name: 'sentence',
    props: {
        // 接收父组件传来的数据
        theme: {
            type: String,
            default: () => 'default'
        }
    },
    data() {
        return {
            timer: null,
            interval: 60, // 刷新频率。单位：秒
            sentences: {
                uuid: null,
                from: '鲁迅',
                statement: '此后如竟没有炬火，我便是唯一的光。'
            }
        };
    },
    mounted() {
        // 当前时间
        this.timer = new Date().getTime();

        // 获取每日一句
        this.getSentences();

        // 每隔N秒获取一次
        this.timer = setInterval(() => {
            // 获取数据
            this.getSentences();
        }, this.interval * 1000);

        this.$emit('on-mounted');
    },
    unmounted() {
        clearInterval(this.timer);
    },
    methods: {
        /**
         * 获取每日一句
         * @returns {Promise<void>}
         */
        async getSentences(el) {
            // 点击时获取元素的内容
            if (el) {
                // 复制到剪切板
                this.$TOOL.toCopyClipboard(el.srcElement.innerText);
            }

            // 指定分类
            const res = await this.$API.common.sentences.get({
                custom: '?c=h&c=i&c=k'
            });

            if (res && res.uuid) {
                this.sentences.statement = res.hitokoto;
                this.sentences.from = res.from_who || res.from;
                this.sentences.uuid = res.uuid;
            }
        }
    }
};
</script>

<style scoped lang="scss"></style>
