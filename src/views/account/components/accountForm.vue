<template>
    <el-form ref="loginForm" :model="form" :rules="rules" label-width="0" size="large" @keyup.enter="login">
        <el-form-item prop="tenant_code">
            <el-input
                v-model="form.tenant_code"
                clearable
                size="large"
                placeholder="请输入油田编码"
                prefix-icon="el-icon-office-building"
                style="width: 100%; height: 42px"
            >
            </el-input>
        </el-form-item>
        <el-form-item prop="username">
            <el-input
                v-model="form.username"
                clearable
                size="large"
                placeholder="请输入用户名"
                prefix-icon="el-icon-user"
                style="width: 100%; height: 42px"
            >
            </el-input>
        </el-form-item>
        <el-form-item prop="password">
            <el-input
                v-model="form.password"
                clearable
                size="large"
                placeholder="请输入密码"
                prefix-icon="el-icon-lock"
                show-password
                style="width: 100%; height: 42px"
            ></el-input>
        </el-form-item>
        <!--<el-form-item prop="captcha">
            <el-input
                v-model="form.captcha"
                clearable
                size="large"
                placeholder="请输入验证码"
                prefix-icon="el-icon-key"
                style="width: 100%; height: 42px"
            >
            </el-input>
        </el-form-item>-->
        <el-form-item style="margin-bottom: 10px" v-if="!dialogMode">
            <!--<el-col :span="12">
                <el-checkbox v-model="form.autologin" label="记住密码"></el-checkbox>
                <a href="javascript:;" style="color: #6b6b6b">注册新用户</a>
            </el-col>
            <el-col :span="12" class="login-forgot">
                <router-link to="/reset_password">忘记密码？</router-link>
            </el-col>-->
        </el-form-item>
        <el-form-item>
            <el-button
                :loading="loading"
                :round="false"
                :color="dialogMode ? '#339257' : '#3f3648'"
                style="width: 100%; height: 42px; filter: opacity(0.95)"
                type="primary"
                @click="login"
                >登 录</el-button
            >
        </el-form-item>
        <!--<div class="login-reg">
            还没有账号? <router-link to="/user_register">创建新账号</router-link>
        </div>-->
    </el-form>
</template>

<script>
import account from '@/utils/account';

export default {
    props: {
        // 是否对话框模式（用于重新登录时传入对应参数）
        dialogMode: {
            type: Boolean,
            default: () => {
                return false;
            }
        }
    },
    data() {
        return {
            loading: false,
            form: {
                login_type: 'account',
                tenant_code: '',
                username: '',
                password: '',
                captcha: '123456',
            },
            rules: {
                tenant_code: [{ required: true, message: '请输入油田编码', trigger: 'blur' }],
                username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
                password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
                captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
            }
        };
    },
    watch: {},
    mounted() {},
    methods: {
        async login() {
            const validate = await this.$refs.loginForm.validate().catch(() => {});
            if (!validate) {
                return false;
            }

            this.loading = true;

            const data = {
                login_type: this.form.login_type,
                tenant_code: this.form.tenant_code,
                username: this.form.username,
                password: this.$TOOL.crypto.MD5(this.form.password).toLowerCase(),
                captcha: this.form.captcha,
            };

            try {
                // 账号密码登录
                const res = await this.$API.account.login.post(data);

                if (res && res.status === 1) {
                    // 是否重定向
                    const _redirect = !this.dialogMode;

                    // 任意类型登录后置操作封装
                    await account.loginSuccess(res, _redirect);

                    this.$message.success('登录成功');
                } else {
                    this.$message.error(res.message);
                }
            } catch (err) {
                console.error('登录失败：', err);
                this.$message.error('登录失败,请重试');
            } finally {
                // 隐藏掉loading
                this.loading = false;
            }
        }
    }
};
</script>

<style></style>
