<template>
    <div :class="{ login_body: true, dialog_mode: dialogMode, is_login: isLogin }" :dialog-mode="dialogMode">
        <div class="login_left" v-if="!dialogMode">
            <div class="profile-body">
                <i class="logo"></i>
                <h4>高效 / 精致 / 优雅</h4>
                <p>坚持诚信为本，用心打造行业精品</p>
                <!--<div class="profile-main">
                    <label>有序规划和管理软件研发流程</label>
                    <label>坚持诚信为本，用心打造行业精品</label>
                </div>-->
                <div class="sentences-main">
                    <!--引入简言组件-->
                    <sentences :theme="'classic'"></sentences>
                </div>
            </div>
            <div class="login_left__mask"></div>
            <div class="login_left__bottom">© version {{ $CONFIG.APP_VER }}</div>
        </div>
        <div class="login_main">
            <div class="login-type-switch" v-if="!isLogin && !dialogMode && qrcodeLogin">
                <div class="tab-item active" data-tab-for="qrcode_login" title="扫码登录"
                    ><i class="login-icon login-icon-qrcode"></i
                ></div>
                <div class="tab-item" data-tab-for="account_login" title="帐号登录"
                    ><i class="login-icon login-icon-account"></i
                ></div>
            </div>
            <div class="login-form">
                <div class="login-form-head" v-if="!dialogMode">
                    <!-- Login Title -->
                    <fieldset class="heading-name">
                        <legend>
                            <span>{{ systemTitle }}</span>
                        </legend>
                    </fieldset>
                    <fieldset class="heading-sub">
                        <legend
                            ><span>{{ systemDescription }}</span>
                        </legend>
                    </fieldset>
                </div>
                <!--未登录时-->
                <div v-if="!isLogin">
                    <el-tabs v-if="!systemClose || dialogMode">
                        <el-tab-pane label="账号登录" lazy v-if="accountLogin || dialogMode">
                            <account-form :dialogMode="dialogMode"></account-form>
                        </el-tab-pane>
                        <el-tab-pane label="短信验证登录" lazy v-if="smsVerifyLogin">
                            <phone-form :dialogMode="dialogMode"></phone-form>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <!--非对话框已登录时-->
                <div v-if="isLogin && !dialogMode">
                    <account-info :userInfo="userInfo"></account-info>
                </div>
                <!--对话框模式可以选择退出登录-->
                <div v-if="dialogMode">
                    <el-divider style="margin: 0 0 15px 0"></el-divider>
                    <div class="login-other">
                        <el-link href="javascript:;" style="font-weight: normal" @click="logoutSystem"
                            >退出系统</el-link
                        >
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="login_bg_filter" v-if="!dialogMode"></div>
    <div class="login_bg" v-if="!dialogMode"></div>
</template>

<script>
import account from '@/utils/account';
import accountInfo from './components/accountInfo';
import accountForm from './components/accountForm';
import phoneForm from './components/phoneForm';
import sentences from '@/views/dashboard/widgets/components/sentence';

export default {
    name: 'system_login',
    props: {
        // 登录对话框数据对象（用于重新登录时传入对应参数）
        loginMode: {
            type: String,
            default: () => {
                return 'page';
            }
        }
    },
    components: {
        sentences,
        accountInfo,
        accountForm,
        phoneForm
    },
    data() {
        return {
            // 是否已登录
            isLogin: false,
            // 已登录时的用户信息
            userInfo: {},
            // 是否对话框模式
            dialogMode: false,
            // 是否关闭系统
            systemClose: true,
            // 系统名称
            systemTitle: this.$CONFIG.APP_NAME,
            // 系统描述
            systemDescription: 'Devmix Agile Development System',
            // 是否显示平台二维码登录
            qrcodeLogin: this.$CONFIG.LOGIN.qrcodeLogin || false,
            // 是否显示账号登录
            accountLogin: this.$CONFIG.LOGIN.accountLogin || false,
            // 是否显示短信验证登录
            smsVerifyLogin: this.$CONFIG.LOGIN.smsVerifyLogin || false
        };
    },
    watch: {},
    created() {
        // 是否对话框模式
        this.dialogMode = this.loginMode !== 'page';

        // 非对话框模式时
        if (this.dialogMode === false) {
            // 获取系统配置信息
            this.getSystemInfo();
        }
    },
    mounted() {},
    methods: {
        /**
         * 退出登录系统方式
         */
        logoutSystem() {
            return account.logoutSystem();
        },
        /**
         * 获取系统配置信息
         * @returns {Promise<void>}
         */
        async getSystemInfo() {
            const res = await this.$API.common.system.info.get();

            if (res && res.status === 1) {
                const _config = res.data.config || {};
                const _userInfo = res.data.userinfo || {};

                // 是否关闭系统
                if (!_config.system_close) {
                    this.systemClose = false;
                }

                // 系统名称
                if (_config.system_title) {
                    this.systemTitle = _config.system_title;
                }

                // 系统名称
                if (_config.system_description) {
                    this.systemDescription = _config.system_description;
                }

                // 系统登录方式
                if (_config.system_login_type) {
                    const _loginType = _config.system_login_type.split(',');

                    // 是否账号登录方式
                    if (_loginType.indexOf('account_login') >= 0) {
                        this.accountLogin = true;
                    } else {
                        this.accountLogin = false;
                    }

                    // 是短信验证登录方式
                    if (_loginType.indexOf('sms_login') >= 0) {
                        this.smsVerifyLogin = true;
                    } else {
                        this.smsVerifyLogin = false;
                    }
                }

                if (_userInfo.username) {
                    // 标记为已登录状态
                    this.isLogin = true;

                    this.userInfo = _userInfo;
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
// 已登录状态样式
.is_login {
    .heading-name {
        color: #766a4a !important;
    }
    .heading-sub {
        border-color: rgba(129, 105, 55, 0.25) !important;

        legend {
            color: #766a4a !important;
        }
    }
}
</style>
