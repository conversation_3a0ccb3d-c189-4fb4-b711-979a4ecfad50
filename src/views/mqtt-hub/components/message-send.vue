<template>
    <div class="message-send">
        <h4>发送消息</h4>
        <el-form @submit.prevent="sendMessage" style="margin:0">
            <el-form-item class="custom-form-item">
                <div class="message-header">
                    <span class="topic-label">消息主题</span>
                    <el-autocomplete
                        v-model="selectedTopicId"
                        :fetch-suggestions="queryTopicSuggestions"
                        placeholder="选择或输入发送主题"
                        style="width: 50%"
                        :disabled="!isConnected"
                        :trigger-on-focus="true"
                        clearable
                        @select="handleTopicSelect"
                    >
                        <template #default="{ item }">
                            <div class="topic-option">
                                <span class="topic-text">{{ item.topic }}</span>
                                <el-tag size="small" class="qos-tag" :type="qosTagType(item.qos)">
                                    QoS {{ item.qos }}
                                </el-tag>
                            </div>
                        </template>
                    </el-autocomplete>
                    <el-select
                        v-model="qos"
                        placeholder="QoS"
                        style="width:170px; margin: 0 10px"
                        :disabled="!isConnected"
                    >
                        <el-option :value="0" label="QoS 0（最多一次）" />
                        <el-option :value="1" label="QoS 1（至少一次）" />
                        <el-option :value="2" label="QoS 2（恰好一次）" />
                    </el-select>
                    <el-checkbox v-model="retain" :disabled="!isConnected">
                        Retain
                    </el-checkbox>
                </div>
                <div class="message-input">
                    <div class="input-wrapper">
                        <el-input
                            v-model="messageToSend"
                            placeholder="输入要发送的消息"
                            :disabled="!isConnected || !selectedTopic"
                            type="textarea"
                            :rows="2"
                            resize="none"
                            @keydown.enter="handleKeydown"
                            class="message-textarea"
                        />
                        <el-button
                            type="primary"
                            @click="sendMessage"
                            :disabled="!isConnected || !messageToSend || !selectedTopic"
                            class="send-button"
                        >
                            发送
                        </el-button>
                    </div>
                </div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import mqttService from '@/services/mqttService';
import { ElMessage } from 'element-plus';

export default {
    name: 'MqttMessageSend',
    props: {
        isConnected: {
            type: Boolean,
            required: true
        },
        topics: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            selectedTopicId: '',
            messageToSend: '',
            qos: 0,
            retain: false
        };
    },
    computed: {
        selectedTopic() {
            // 如果在已订阅的主题中找到，返回完整的主题对象
            const existingTopic = this.topics.find(t => t.topic === this.selectedTopicId);
            if (existingTopic) {
                return existingTopic;
            }
            // 如果是自定义输入的主题，创建一个新的主题对象
            if (this.selectedTopicId) {
                return {
                    topic: this.selectedTopicId,
                    qos: this.qos
                };
            }
            return null;
        }
    },
    methods: {
        queryTopicSuggestions(queryString, callback) {
            // 从 mqttService 获取订阅列表
            const subscriptions = mqttService.getSubscriptions();
            const suggestions = subscriptions
                .filter(topic => {
                    const hasSpecialChars = topic.topic.includes('#') ||
                                          topic.topic.includes('+') ||
                                          topic.topic.includes('$');
                    return !hasSpecialChars &&
                           (queryString ? topic.topic.toLowerCase().includes(queryString.toLowerCase()) : true);
                })
                .map(topic => ({
                    value: topic.topic,
                    ...topic
                }));
            callback(suggestions);
        },

        handleTopicSelect(item) {
            if (item) {
                // 检查主题是否包含特殊字符
                if (item.topic.includes('#') || item.topic.includes('+') || item.topic.includes('$')) {
                    ElMessage.warning('不能选择包含通配符 #、+、$ 的主题');
                    this.selectedTopicId = '';
                    return;
                }
                this.qos = item.qos;
            }
        },

        sendMessage() {
            if (!this.messageToSend.trim() || !this.selectedTopic) {
                return;
            }

            if (!mqttService.isConnected) {
                ElMessage.warning('MQTT未连接，无法发送消息');
                return;
            }

            // 检查主题是否包含通配符
            if (this.selectedTopic.topic.includes('#') || this.selectedTopic.topic.includes('+')) {
                ElMessage.error('不能向包含通配符 #、+ 的主题发布消息');
                return;
            }

            mqttService.publish(this.selectedTopic.topic, this.messageToSend, {
                qos: this.qos,
                retain: this.retain
            });

            this.messageToSend = '';
        },

        qosTagType(qos) {
            const types = ['info', 'success', 'warning'];
            return types[qos] || 'info';
        },

        handleKeydown(event) {
            if (event.key === 'Enter' && !event.ctrlKey) {
                event.preventDefault();
                this.sendMessage();
            } else if (event.key === 'Enter' && event.ctrlKey) {
                // 允许换行
            }
        },
    },
    watch: {
        topics: {
            handler(newTopics) {
                if (!this.selectedTopicId || !newTopics.some(t => t.topic === this.selectedTopicId)) {
                    this.selectedTopicId = newTopics[0]?.topic || '';
                }
            },
            immediate: true
        }
    }
};
</script>

<style scoped lang="scss">
.message-send {
    padding: 15px;

    h4 {
        margin-top: 0;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
    }

    .custom-form-item {
        margin-bottom: 0;
        :deep(.el-form-item__content) {
            display: block !important;
        }
    }

    .message-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .topic-label {
            margin-right: 10px;
            font-size: 14px;
            color: #606266;
        }
    }

    .message-input {
        margin-bottom: 0;

        .input-wrapper {
            display: flex;
            align-items: stretch;  // 使子元素高度一致
            gap: 10px;

            .el-textarea {
                flex: 1;
            }

            .send-button {
                height: auto;  // 自动适应父容器高度
                padding-top: 12px;
                padding-bottom: 12px;
            }
        }
    }

    .message-button {
        display: flex;
        justify-content: flex-end;

        .el-button {
            min-width: 100px;
        }
    }

    :deep(.el-textarea__inner) {
        font-family: monospace;
    }
}

.topic-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .topic-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 8px;
        font-family: monospace;
    }

    .qos-tag {
        flex-shrink: 0;
    }
}
</style>
