<template>
    <topic-management
        :is-connected="isConnected"
        @topics-change="handleTopicsChange"
    />
</template>

<script>
import TopicManagement from './topic-management.vue';

export default {
    name: 'MqttAsideMain',
    components: {
        TopicManagement
    },
    props: {
        isConnected: {
            type: Boolean,
            required: true
        }
    },
    methods: {
        handleTopicsChange(topics) {
            this.$emit('topics-change', topics);
        }
    }
};
</script>

<style scoped lang="scss">
</style>
