<template>
    <div class="message-list received">
        <h4>接收到的消息</h4>
        <div v-if="messages.length === 0" class="no-messages">
            <el-empty :image-size="280" image="img/not-found.png" description="暂无接收消息" />
        </div>
        <div v-else class="messages-container">
            <el-scrollbar ref="scrollbar" @scroll="handleScroll">
                <div v-for="(msg, index) in messages" :key="index" class="message-item">
                    <div class="message-avatar received-avatar"></div>
                    <div class="message-time">
                        <span>{{ formatTime(msg.time) }}</span>
                        <span class="client-id" v-if="msg.clientId">来自: {{ msg.clientId }}</span>
                    </div>
                    <div class="message-info">
                        <el-tag size="small" type="info" class="message-topic" v-if="msg.topic">{{ msg.topic }}</el-tag>
                        <el-tag size="small" type="success" class="message-qos" v-if="msg.qos !== undefined">QoS: {{ msg.qos }}</el-tag>
                    </div>
                    <div class="message-content">{{ msg.content }}</div>
                </div>
            </el-scrollbar>
            <div v-if="showNewMessageNotice" class="new-message-notice" @click="handleNewMessageClick">
                {{ newMessageCount }}条新消息
            </div>
        </div>
    </div>
</template>

<script>
import { formatTimestamp } from '@/utils/date';

export default {
    name: 'ReceivedMessages',
    props: {
        messages: {
            type: Array,
            required: true,
            default: () => []
        }
    },
    data() {
        return {
            isAtBottom: true,
            showNewMessageNotice: false,
            newMessageCount: 0,
            lastMessageCount: 0
        };
    },
    mounted() {
        // 组件挂载后，滚动到底部
        this.$nextTick(() => {
            this.scrollToBottom(false);
        });
    },
    updated() {
        // 每次组件更新后，检查是否需要滚动到底部
        this.$nextTick(() => {
            if (this.isAtBottom && !this.showNewMessageNotice) {
                this.scrollToBottom(false);
            }
        });
    },
    watch: {
        messages(newMessages, oldMessages) {
            if (newMessages.length > oldMessages.length) {
                const addedCount = newMessages.length - oldMessages.length;

                // 检查滚动条是否在底部
                const scrollbar = this.$refs.scrollbar;
                if (scrollbar) {
                    const { scrollTop, scrollHeight, clientHeight } = scrollbar.wrapRef;
                    this.isAtBottom = (scrollHeight - scrollTop - clientHeight) < 5;

                    if (this.isAtBottom) {
                        // 如果在底部，滚动到新消息
                        this.$nextTick(() => {
                            this.scrollToBottom(false);
                        });
                    } else {
                        // 如果不在底部，显示新消息提示
                        this.newMessageCount += addedCount;
                        this.showNewMessageNotice = true;
                    }
                }
            }
        }
    },
    methods: {
        formatTime(timestamp, format = 'YYYY/MM/DD HH:mm:ss') {
            return formatTimestamp(timestamp, format);
        },
        handleNewMessageClick() {
            // 点击新消息提示按钮时，使用平滑滚动效果
            this.smoothScrollToBottom();
        },
        handleScroll(e) {
            // 获取滚动容器的高度信息
            const scrollContainer = this.$refs.scrollbar.wrapRef;
            const scrollTop = scrollContainer.scrollTop;
            const scrollHeight = scrollContainer.scrollHeight;
            const clientHeight = scrollContainer.clientHeight;

            // 计算到底部的距离
            const distanceToBottom = scrollHeight - scrollTop - clientHeight;

            // 距离底部5px内都算作"到底部"
            const isAtBottom = distanceToBottom <= 5;

            // 更新底部状态
            this.isAtBottom = isAtBottom;

            // 调试
            /* console.log('滚动信息:', {
                scrollTop,
                scrollHeight,
                clientHeight,
                distanceToBottom,
                isAtBottom
            }); */

            // 如果滚动到底部，立即清除新消息提示
            if (isAtBottom) {
                this.showNewMessageNotice = false;
                this.newMessageCount = 0;
            }
        },
        scrollToBottom(smooth = true) {
            const scrollbar = this.$refs.scrollbar;
            if (scrollbar && scrollbar.wrapRef) {
                // 获取滚动容器
                const scrollContainer = scrollbar.wrapRef;

                // 滚动到底部
                scrollContainer.scrollTo({
                    top: scrollContainer.scrollHeight,
                    behavior: smooth ? 'smooth' : 'auto'
                });

                // 设置状态
                this.isAtBottom = true;
                this.showNewMessageNotice = false;
                this.newMessageCount = 0;
            }
        },
        smoothScrollToBottom() {
            const scrollbar = this.$refs.scrollbar;
            if (scrollbar && scrollbar.wrapRef) {
                const container = scrollbar.wrapRef;
                const startPosition = container.scrollTop;
                const targetPosition = container.scrollHeight - container.clientHeight;
                const distance = targetPosition - startPosition;

                // 如果距离过小，直接滚动到底部
                if (distance < 50) {
                    this.scrollToBottom(false);
                    return;
                }

                // 平滑滚动动画
                const duration = 300; // 动画持续时间（毫秒）
                const startTime = performance.now();

                const animateScroll = (currentTime) => {
                    const elapsedTime = currentTime - startTime;

                    // 使用缓动函数计算滚动位置
                    const progress = Math.min(elapsedTime / duration, 1);
                    const easeProgress = this.easeInOutCubic(progress);
                    const currentPosition = startPosition + distance * easeProgress;

                    container.scrollTop = currentPosition;

                    // 如果动画未完成，继续动画
                    if (elapsedTime < duration) {
                        requestAnimationFrame(animateScroll);
                    } else {
                        // 动画完成，确保滚动到底部
                        container.scrollTop = targetPosition;
                        this.isAtBottom = true;
                        this.showNewMessageNotice = false;
                        this.newMessageCount = 0;
                    }
                };

                // 开始动画
                requestAnimationFrame(animateScroll);
            }
        },

        // 缓动函数
        easeInOutCubic(t) {
            return t < 0.5
                ? 4 * t * t * t
                : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
        }
    }
};
</script>

<style scoped lang="scss">
.message-list {
    padding: 15px;
    background-color: var(--el-bg-color);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;

    .messages-container {
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 300px;
        transition: all 0.3s ease;
    }

    h4 {
        margin-top: 0;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--el-border-color-light);
        flex-shrink: 0;
        font-weight: 500;
        color: var(--el-text-color-primary);
    }

    .el-scrollbar {
        flex: 1;
        padding: 10px 0;
    }

    &.received {
        background-color: var(--el-color-info-light-9);
        &:hover {
            border-color: var(--el-color-info-light-5);
        }
    }
}

.message-item {
    padding: 8px 12px;
    margin: 10px 0;
    position: relative;
}

.message-time {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .client-id {
        color: var(--el-color-primary);
        margin-left: 10px;
    }
}

.message-info {
    margin-bottom: 8px;
    margin-left: 60px;
    .message-topic,
    .message-qos {
        margin-right: 8px;
    }
}

.message-content {
    word-break: break-all;
    padding: 10px 15px;
    border-radius: 8px;
    position: relative;
    max-width: 80%;
    display: inline-block;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.received {
    .message-avatar {
        left: 10px;
        background-image: url('@/assets/img/avatar-a.svg');
    }

    .message-time,
    .message-content {
        margin-left: 60px;
    }

    .message-content {
        background-color: #ffffff;

        &::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-right: 8px solid #ffffff;
        }
    }
}

.no-messages {
    color: var(--el-text-color-secondary);
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    border-radius: 4px;
    margin: 10px 0;

    :deep(.el-empty__image) {
        opacity: 0.25;
    }
}

.new-message-notice {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--el-color-primary);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
    transition: all 0.3s ease;

    &:hover {
        background-color: var(--el-color-primary-light-3);
    }
}
</style>
