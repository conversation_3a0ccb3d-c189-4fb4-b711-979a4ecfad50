<template>
    <el-header>
        <div class="left-panel">
        </div>
        <div class="right-panel">
            <div class="right-panel-search">
                <el-input
                    style="min-width: 300px"
                    v-model="searchObj.keyword"
                    clearable
                    :placeholder="logType === 'login' ? '登录消息' : '操作消息'"
                    @keyup.enter="searchHandle"
                    @clear="searchHandle"
                ></el-input>
                <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="searchHandle"></el-button>
            </div>
        </div>
    </el-header>
    <el-main class="p0">
        <scTable
            ref="dataTable"
            :apiObj="dataObj"
            :pageSize="pageSize"
            :params="searchObj"
            remoteFilter
            remoteSort
            row-key="id"
            stripe
            class="custom-list-table"
        >
            <el-table-column label="ID" prop="id" sortable="custom" width="80"></el-table-column>
            <template v-if="logType === 'login'">
                <!-- 登录日志字段 -->
                <el-table-column label="用户账号" show-overflow-tooltip prop="username" width="120"></el-table-column>
                <el-table-column label="登录方式" show-overflow-tooltip prop="login_type" width="100">
                    <template #default="scope">
                        <el-tag v-if="scope.row.login_type === 'account'" type="primary">账号密码</el-tag>
                        <el-tag v-else-if="scope.row.login_type === 'mobile'" type="success">手机验证码</el-tag>
                        <el-tag v-else>{{scope.row.login_type}}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="登录消息" show-overflow-tooltip prop="title" width="150"></el-table-column>
                <el-table-column label="登录IP" prop="ip" width="150"></el-table-column>
                <el-table-column label="浏览器信息" show-overflow-tooltip prop="user_agent" width="200"></el-table-column>
                <el-table-column label="登录时间" prop="login_time" sortable="custom" width="160"></el-table-column>
                <el-table-column label="状态" prop="status" width="80">
                    <template #default="scope">
                        <el-tag v-if="scope.row.status === 1" type="success">成功</el-tag>
                        <el-tag v-else type="danger">失败</el-tag>
                    </template>
                </el-table-column>
            </template>
            <template v-else>
                <!-- 操作日志字段 -->
                <el-table-column label="用户账号" show-overflow-tooltip prop="username" width="120"></el-table-column>
                <el-table-column label="操作消息" show-overflow-tooltip prop="title" width="150"></el-table-column>
                <el-table-column label="请求路径" show-overflow-tooltip prop="path" width="200"></el-table-column>
                <el-table-column label="请求方法" prop="method" width="100">
                    <template #default="scope">
                        <el-tag v-if="scope.row.method === 'GET'" type="success">GET</el-tag>
                        <el-tag v-else-if="scope.row.method === 'POST'" type="primary">POST</el-tag>
                        <el-tag v-else-if="scope.row.method === 'PUT'" type="warning">PUT</el-tag>
                        <el-tag v-else-if="scope.row.method === 'DELETE'" type="danger">DELETE</el-tag>
                        <el-tag v-else>{{scope.row.method}}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="IP地址" prop="ip" width="150"></el-table-column>
                <el-table-column label="操作时间" prop="operate_time" sortable="custom" width="160"></el-table-column>
                <el-table-column label="状态" prop="status" width="80">
                    <template #default="scope">
                        <el-tag v-if="scope.row.status === 1" type="success">成功</el-tag>
                        <el-tag v-else type="danger">失败</el-tag>
                    </template>
                </el-table-column>
            </template>
            <el-table-column align="center" fixed="right" label="操作" width="80">
                <template #default="scope">
                    <el-button size="small" type="success" @click="log_view(scope.row, scope.$index)" color="#28a745"
                        >详细</el-button
                    >
                </template>
            </el-table-column>
        </scTable>
    </el-main>

    <!--日志详情-->
    <el-drawer
        v-model="info_dialog"
        :size="800"
        custom-class="drawerBG"
        destroy-on-close
        direction="rtl"
        title="日志详细信息"
    >
        <log-info ref="logInfoRef" :logType="logType"></log-info>
    </el-drawer>
</template>

<script>
import logInfo from '../logs/info';

export default {
    name: 'user-log-list',
    components: {
        logInfo
    },
    props: {
        logType: {
            type: String,
            default: 'login', // 默认为登录日志
            validator: (value) => ['login', 'operation'].includes(value)
        },
        pageSize: {
            type: Number,
            default: 10
        }
    },
    data() {
        return {
            info_dialog: false,
            dataObj: this.$API.account.logs,
            searchObj: {
                type: this.logType, // 初始化时设置日志类型
            },
            userList: []
        };
    },
    methods: {
        log_view(row) {
            this.info_dialog = true;
            this.$nextTick(() => {
                this.$refs.logInfoRef.setData(row);
            });
        },
        searchHandle() {
            // 状态已经是数组格式，直接传递
            this.$refs.dataTable.upData(this.searchObj);
        }
    }
};
</script>

<style scoped></style>
