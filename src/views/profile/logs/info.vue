<template>
    <el-main style="padding: 0 20px">
        <el-card shadow="never">
            <el-descriptions :column="1" border title="">
                <el-descriptions-item label="日志ID" min-width="150px">{{ logInfo.id }}</el-descriptions-item>
                <el-descriptions-item label="租户ID">{{ logInfo.tenant_id }}</el-descriptions-item>
                <el-descriptions-item label="用户ID">{{ logInfo.user_id }}</el-descriptions-item>
                <el-descriptions-item label="用户账号">{{ logInfo.username }}</el-descriptions-item>
                
                <!-- 登录日志特有字段 -->
                <template v-if="logType === 'login'">
                    <el-descriptions-item label="登录方式">
                        <el-tag v-if="logInfo.login_type === 'account'" type="primary">账号密码</el-tag>
                        <el-tag v-else-if="logInfo.login_type === 'mobile'" type="success">手机验证码</el-tag>
                        <el-tag v-else>{{ logInfo.login_type }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="登录消息">{{ logInfo.title }}</el-descriptions-item>
                    <el-descriptions-item label="登录IP">{{ logInfo.ip }}</el-descriptions-item>
                    <el-descriptions-item label="浏览器信息">{{ logInfo.user_agent }}</el-descriptions-item>
                    <el-descriptions-item label="请求ID">{{ logInfo.request_id }}</el-descriptions-item>
                    <el-descriptions-item label="客户端ID">{{ logInfo.client_id || '无' }}</el-descriptions-item>
                    <el-descriptions-item label="客户端指纹">{{ logInfo.finger_id }}</el-descriptions-item>
                    <el-descriptions-item label="登录时间">{{ logInfo.login_time }}</el-descriptions-item>
                    <el-descriptions-item label="登录状态">
                        <el-tag v-if="logInfo.status === 1" type="success">成功</el-tag>
                        <el-tag v-else type="danger">失败</el-tag>
                    </el-descriptions-item>
                </template>
                
                <!-- 操作日志特有字段 -->
                <template v-else>
                    <el-descriptions-item label="操作消息">{{ logInfo.title }}</el-descriptions-item>
                    <el-descriptions-item label="请求路径">{{ logInfo.path }}</el-descriptions-item>
                    <el-descriptions-item label="请求方法">
                        <el-tag v-if="logInfo.method === 'GET'" type="success">GET</el-tag>
                        <el-tag v-else-if="logInfo.method === 'POST'" type="primary">POST</el-tag>
                        <el-tag v-else-if="logInfo.method === 'PUT'" type="warning">PUT</el-tag>
                        <el-tag v-else-if="logInfo.method === 'DELETE'" type="danger">DELETE</el-tag>
                        <el-tag v-else>{{ logInfo.method }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="操作内容">
                        <pre>{{ formatJSON(logInfo.payload) }}</pre>
                    </el-descriptions-item>
                    <el-descriptions-item label="IP地址">{{ logInfo.ip }}</el-descriptions-item>
                    <el-descriptions-item label="请求ID">{{ logInfo.request_id }}</el-descriptions-item>
                    <el-descriptions-item label="客户端ID">{{ logInfo.client_id || '无' }}</el-descriptions-item>
                    <el-descriptions-item label="客户端指纹">{{ logInfo.finger_id }}</el-descriptions-item>
                    <el-descriptions-item label="相关数据" v-if="logInfo.data">
                        <pre>{{ formatJSON(logInfo.data) }}</pre>
                    </el-descriptions-item>
                    <el-descriptions-item label="操作时间">{{ logInfo.operate_time }}</el-descriptions-item>
                    <el-descriptions-item label="操作状态">
                        <el-tag v-if="logInfo.status === 1" type="success">成功</el-tag>
                        <el-tag v-else type="danger">失败</el-tag>
                    </el-descriptions-item>
                </template>
            </el-descriptions>
        </el-card>
    </el-main>
</template>

<script>
export default {
    name: 'log-info',
    props: {
        logType: {
            type: String,
            default: 'login',
            validator: (value) => ['login', 'operation'].includes(value)
        }
    },
    data() {
        return {
            logInfo: {}
        };
    },
    methods: {
        setData(row) {
            this.logInfo = row;
        },
        formatJSON(data) {
            if (!data) return '';
            
            try {
                if (typeof data === 'string') {
                    return JSON.stringify(JSON.parse(data), null, 2);
                } else {
                    return JSON.stringify(data, null, 2);
                }
            } catch (e) {
                return data;
            }
        }
    }
};
</script>

<style scoped>
pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    background-color: #f5f7fa;
    padding: 8px;
    border-radius: 4px;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    max-height: 300px;
    overflow-y: auto;
}
</style>
