// 路由元信息类型
export interface RouteMeta {
    title: string;
    icon?: string;
    type?: 'menu' | 'button' | 'link' | 'iframe';
    hidden?: boolean;
    role?: string[];
    keepAlive?: boolean;
    affix?: boolean;
    breadcrumb?: boolean;
    activeMenu?: string;
}

// 路由配置类型
export interface RouteConfig {
    name: string;
    path: string;
    meta: RouteMeta;
    component: string;
    redirect?: string;
    children?: RouteConfig[];
}

// 静态路由配置
// 书写格式与动态路由格式一致，全部经由框架统一转换
// 比较动态路由在meta中多加入了role角色权限，为数组类型。一个菜单是否有权限显示，取决于它以及后代菜单是否有权限。
// routes 显示在左侧菜单中的路由(显示顺序在动态路由之前)

const routes: RouteConfig[] = [
    {
        name: 'profile',
        path: '/profile/index',
        meta: {
            title: '用户中心',
            icon: 'el-icon-user',
            type: 'menu',
            hidden: true
        },
        component: 'profile/index'
    },
    {
        name: 'notification',
        path: '/notification/index',
        meta: {
            title: '消息通知',
            icon: 'el-icon-comment',
            type: 'menu',
            hidden: true
        },
        component: 'notification/index'
    },
    {
        name: 'document',
        path: '/document/index',
        meta: {
            title: 'API文档',
            icon: 'sc-icon-apidoc',
            type: 'menu',
            hidden: true
        },
        component: 'document/index'
    },
    {
        name: 'mqtt-hub',
        path: '/mqtt-hub',
        meta: {
            title: 'MQHub',
            icon: 'sc-icon-mqtt',
            type: 'menu',
            hidden: true
        },
        component: 'mqtt-hub/index'
    },
];

export default routes;
