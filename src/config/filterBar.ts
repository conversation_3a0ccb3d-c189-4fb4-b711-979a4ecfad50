// 运算符选项类型
export interface OperatorOption {
    label: string;
    value: string;
}

// 过滤条件配置类型
export interface FilterBarConfig {
    operator: OperatorOption[];
    separator: string;
    valueFormat: string;
    getMy: (name: string) => Promise<any[]>;
    saveMy: (name: string, obj: any) => Promise<boolean>;
    delMy: (name: string) => Promise<boolean>;
}

const filterBarConfig: FilterBarConfig = {
    // 运算符
    operator: [
        {
            label: '等于',
            value: '='
        },
        {
            label: '不等于',
            value: '!='
        },
        {
            label: '大于',
            value: '>'
        },
        {
            label: '大于等于',
            value: '>='
        },
        {
            label: '小于',
            value: '<'
        },
        {
            label: '小于等于',
            value: '<='
        },
        {
            label: '包含',
            value: 'include'
        },
        {
            label: '不包含',
            value: 'notinclude'
        }
    ],
    // 过滤结果运算符的分隔符
    separator: '|',
    // 返回值的格式
    valueFormat: '{key}:{value}{separator}{operator}',
    // 获取我的常用
    getMy: function (name: string): Promise<any[]> {
        return new Promise(resolve => {
            console.log(`这里可以根据${name}参数请求接口`);
            const list: any[] = [];
            setTimeout(() => {
                resolve(list);
            }, 500);
        });
    },
    /**
     * 常用保存处理 返回resolve后继续操作
     * @param name scFilterBar组件的props->filterName
     * @param obj 过滤项整理好的对象
     */
    saveMy: function (name: string, obj: any): Promise<boolean> {
        return new Promise(resolve => {
            console.log(name, obj);
            setTimeout(() => {
                resolve(true);
            }, 500);
        });
    },
    /**
     * 常用删除处理 返回resolve后继续操作
     * @param name scFilterBar组件的props->filterName
     */
    delMy: function (name: string): Promise<boolean> {
        return new Promise(resolve => {
            console.log(name);
            setTimeout(() => {
                resolve(true);
            }, 500);
        });
    }
};

export default filterBarConfig;
