// 表格选择器解析结果类型
export interface TableSelectParseResult {
    data: any;
    rows: any[];
    total: number;
    msg: string;
    code: number;
}

// 表格选择器请求配置类型
export interface TableSelectRequestConfig {
    page: string;
    pageSize: string;
    keyword: string;
}

// 表格选择器属性配置类型
export interface TableSelectPropsConfig {
    label: string;
    value: string;
}

// 表格选择器配置类型
export interface TableSelectConfig {
    pageSize: number;
    parseData: (res: any) => TableSelectParseResult;
    request: TableSelectRequestConfig;
    props: TableSelectPropsConfig;
}

// 表格选择器配置
const tableSelectConfig: TableSelectConfig = {
    pageSize: 20, // 表格每一页条数
    parseData: function (res: any): TableSelectParseResult {
        return {
            data: res.data,
            rows: res.data.rows, // 分析行数据字段结构
            total: res.data.total, // 分析总数字段结构
            msg: res.message, // 分析描述字段结构
            code: res.code // 分析状态字段结构
        };
    },
    request: {
        page: 'page', // 规定当前分页字段
        pageSize: 'pageSize', // 规定一页条数字段
        keyword: 'keyword' // 规定搜索字段
    },
    props: {
        label: 'label', // 映射label显示字段
        value: 'value' // 映射value值字段
    }
};

export default tableSelectConfig;
