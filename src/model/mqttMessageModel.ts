import { closeDatabase, db } from '@/utils/indexedDB';
import storage from '@/config/storage';
import mqttConfig from '@/config/mqtt';

// MQTT消息类型定义
export interface MqttMessage {
    id?: number;
    topic: string;
    payload: string | Buffer;
    qos?: number;
    retain?: boolean;
    time: number;
    timestamp?: string;
    [key: string]: any;
}

// 存储对象名称
const RECEIVED_STORE_NAME: string = storage.indexedDB.storeConfigs.mqttReceived.name;
const SENT_STORE_NAME: string = storage.indexedDB.storeConfigs.mqttSent.name;

/**
 * MQTT消息数据库模型
 */
class MqttMessageModel {
    // 初始化 Promise
    private initPromise: Promise<void> | null = null;

    /**
     * 构造函数
     */
    constructor() {
        if (mqttConfig.messagePersistence.enableLogging) {
            console.log('初始化MQTT数据库模型');
        }
        // 初始化 Promise
        this.initPromise = Promise.resolve();
    }

    /**
     * 获取接收的消息列表
     * @returns {Promise<MqttMessage[]>} 接收的消息列表
     */
    async getReceivedMessages(): Promise<MqttMessage[]> {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            // 使用 db 的 getAll 方法获取所有接收消息
            return await db.getAll(RECEIVED_STORE_NAME);
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                console.error('获取接收消息失败:', error);
            }
            return [];
        }
    }

    /**
     * 获取发送的消息列表
     * @returns {Promise<MqttMessage[]>} 发送的消息列表
     */
    async getSentMessages(): Promise<MqttMessage[]> {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            // 使用 db 的 getAll 方法获取所有发送消息
            return await db.getAll(SENT_STORE_NAME);
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                console.error('获取发送消息失败:', error);
            }
            return [];
        }
    }

    /**
     * 添加接收的消息
     * @param {MqttMessage} message 消息对象
     * @returns {Promise<IDBValidKey | null>} 添加的消息的键
     */
    async addReceivedMessage(message: MqttMessage): Promise<IDBValidKey | null> {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            //console.log(`添加接收消息:`, message);

            // 使用 db 的 add 方法添加接收消息
            const result = await db.add(RECEIVED_STORE_NAME, message);

            // 获取所有接收消息
            const allMessages = await this.getReceivedMessages();

            // 检查是否超过配置的最大消息数量
            const maxMessages = mqttConfig.messagePersistence.maxMessages;
            if (allMessages.length >= maxMessages) {
                //console.log(`接收消息数量超过${maxMessages}条，删除旧消息`);

                // 按时间排序
                allMessages.sort((a, b) => (b.id || 0) - (a.id || 0));

                // 获取要删除的消息ID
                const idsToDelete = allMessages.slice(maxMessages).map(msg => msg.id).filter(id => id !== undefined);

                // 删除旧消息
                for (const id of idsToDelete) {
                    await db.delete(RECEIVED_STORE_NAME, id);
                }
            }

            return result;
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                console.error('添加接收消息失败:', error);
            }
            return null;
        }
    }

    /**
     * 添加发送的消息
     * @param {MqttMessage} message 消息对象
     * @returns {Promise<IDBValidKey | null>} 添加的消息的键
     */
    async addSentMessage(message: MqttMessage): Promise<IDBValidKey | null> {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            //console.log(`添加发送消息:`, message);

            // 使用 db 的 add 方法添加发送消息
            const result = await db.add(SENT_STORE_NAME, message);

            // 获取所有发送消息
            const allMessages = await this.getSentMessages();

            // 检查是否超过配置的最大消息数量
            const maxMessages = mqttConfig.messagePersistence.maxMessages;
            if (allMessages.length >= maxMessages) {
                //console.log(`发送消息数量超过${maxMessages}条，删除旧消息`);

                // 按时间排序
                allMessages.sort((a, b) => (b.id || 0) - (a.id || 0));

                // 获取要删除的消息ID
                const idsToDelete = allMessages.slice(maxMessages).map(msg => msg.id).filter(id => id !== undefined);

                // 删除旧消息
                for (const id of idsToDelete) {
                    await db.delete(SENT_STORE_NAME, id);
                }
            }

            return result;
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                console.error('添加发送消息失败:', error);
            }
            return null;
        }
    }

    /**
     * 关闭数据库连接
     */
    close(): void {
        closeDatabase();
    }
}

// 创建数据库实例
const mqttMessageModel = new MqttMessageModel();

// 导出数据库实例
export default mqttMessageModel;
