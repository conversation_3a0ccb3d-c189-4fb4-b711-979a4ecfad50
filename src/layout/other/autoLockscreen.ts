import type { ComponentOptions } from 'vue';
import storageConfig from '@/config/storage';

// 组件数据类型
interface AutoLockscreenData {
    logoutCount: string | number | null;
}

// 组件实例类型
interface AutoLockscreenInstance {
    $TOOL: any;
    logoutCount: string | number | null;
}

// 简单的锁屏管理器（heli分支暂不支持锁屏功能）
const lockScreenManager = {
    setupAutoLock(timeout: number): void {
        console.log(`设置自动锁屏时间: ${timeout}分钟`);
        // TODO: 实现自动锁屏功能
    },
    clearAutoLock(): void {
        console.log('清除自动锁屏');
        // TODO: 清除自动锁屏功能
    }
};

const autoLockscreenComponent: ComponentOptions = {
    render() {},
    data(): AutoLockscreenData {
        return {
            logoutCount: (this as any).$TOOL.data.get(storageConfig.vars.autoLockscreen)
        };
    },
    mounted(this: AutoLockscreenInstance) {
        if (this.logoutCount) {
            lockScreenManager.setupAutoLock(parseInt(String(this.logoutCount)));
        }
    },
    unmounted() {
        lockScreenManager.clearAutoLock();
    }
};

export default autoLockscreenComponent;
