/**
 * @description 自动import导入所有 api 模块 - Heli分支（IoT多租户版本）
 */

// 导入具体的API模块
import accountAPI from './model/account';
import commonAPI from './model/common';
import tenantAPI from './model/tenant';  // heli分支特有：租户+设备管理
import monitorAPI from './model/monitor';
import notificationAPI from './model/notification';

// API模块集合类型
export interface APIModules {
    account: typeof accountAPI;
    common: typeof commonAPI;
    tenant: typeof tenantAPI;  // heli分支特有
    monitor: typeof monitorAPI;
    notification: typeof notificationAPI;
}

// 导出API模块集合
const modules: APIModules = {
    account: accountAPI,
    common: commonAPI,
    tenant: tenantAPI,  // heli分支特有
    monitor: monitorAPI,
    notification: notificationAPI
};

// 导出类型
export type { 
    // 基础类型
    ApiResult,
    ApiListResult,
    PaginationParams,
    BaseQuery,
    
    // 业务类型
    User,
    MenuItem,
    Tenant,
    Device,  // heli分支特有
    DeviceInfo,  // heli分支特有
    DeviceLayer,  // heli分支特有
    DeviceHistoryData,  // heli分支特有
    Notification,
    UploadFile,
    UploadResponse,
    
    // API接口类型
    AccountAPI,
    CommonAPI,
    TenantAPI,  // heli分支特有
    MonitorAPI,
    NotificationAPI
} from './types';

export default modules;
