import http from '@/utils/request';

const tenantAPI = {
    manage: {
        list: {
            url: `/tenant/manage/list`,
            name: '获取租户列表',
            get: async function (params: any) {
                try {
                    return await http.get(this.url, params);
                } catch (e) {
                    console.error('租户列表请求失败:', e);
                    return { code: 500, message: '租户列表获取失败，请检查网络连接' };
                }
            }
        },
        add: {
            url: `/tenant/manage/add`,
            name: '添加租户',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        },
        edit: {
            url: `/tenant/manage/edit`,
            name: '编辑租户',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        },
        delete: {
            url: `/tenant/manage/delete`,
            name: '租户软删除',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        },
        batchDelete: {
            url: `/tenant/manage/batchDelete`,
            name: '批量删除租户',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        },
        status: {
            url: `/tenant/manage/status`,
            name: '设置租户状态（禁用启用）',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        },
        sort: {
            url: `/tenant/manage/sort`,
            name: '设置租户排序',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        }
    },
    menu: {
        list: {
            url: `/tenant/menu/list`,
            name: '获取节点列表',
            get: async function () {
                return await http.post(this.url);
            }
        },
        add: {
            url: `/tenant/menu/add`,
            name: '添加节点',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/tenant/menu/edit`,
            name: '编辑节点',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        delete: {
            url: `/tenant/menu/delete`,
            name: '节点软删除',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        status: {
            url: `/tenant/menu/status`,
            name: '设置节点状态（禁用启用）',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        }
    },
    // 设备管理模块（heli分支特有）
    device: {
        myList: {
            url: `/tenant/device/myList`,
            name: '获取当前用户有权控制的设备列表',
            get: async function (data: any = {}) {
                return await http.post(this.url, data);
            }
        },
        list: {
            url: `/tenant/device/list`,
            name: '设备列表',
            get: async function (data: any = {}) {
                return await http.post(this.url, data);
            }
        },
        info: {
            url: `/tenant/device/info`,
            name: '获取当前设备信息',
            get: async function (data: any = {}) {
                return await http.post(this.url, data);
            }
        },
        add: {
            url: `/tenant/device/add`,
            name: '添加设备',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/tenant/device/edit`,
            name: '编辑设备',
            put: async function (params: any) {
                return await http.put(this.url, params);
            }
        },
        delete: {
            url: `/tenant/device/delete`,
            name: '删除设备',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        status: {
            url: `/tenant/device/status`,
            name: '设置设备状态',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        batchDelete: {
            url: `/tenant/device/batchDelete`,
            name: '批量删除设备',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        batchStatus: {
            url: `/tenant/device/batchStatus`,
            name: '批量设置设备状态',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        historyData: {
            url: `/tenant/device/historyData`,
            name: '获取设备历史数据',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        historyDataExport: {
            url: `/tenant/device/historyDataExport`,
            name: '导出设备历史数据',
            post: async function (params: any) {
                return await http.post(this.url, params, { responseType: 'blob' });
            }
        }
    },
    dic: {
        tree: {
            url: `/tenant/dic/tree`,
            name: '获取字典树',
            get: async function () {
                return await http.get(this.url);
            }
        },
        list: {
            url: `/tenant/dic/list`,
            name: '字典明细',
            get: async function (params: any) {
                return await http.get(this.url, params);
            }
        },
        get: {
            url: `/tenant/dic/get`,
            name: '获取字典数据',
            get: async function (params: any) {
                return await http.get(this.url, params);
            }
        }
    }
};

export default tenantAPI;
