import http from '@/utils/request';

// 账户相关接口参数类型
export interface SendYzmParams {
    phone?: string;
    email?: string;
    type: 'sms' | 'email';
}

export interface ChangePasswordParams {
    oldPassword?: string;
    newPassword: string;
    confirmPassword: string;
    force?: boolean;
}

export interface LockscreenParams {
    password: string;
}

export interface RefreshTokenParams {
    refreshToken: string;
}

export interface VerifyTokenParams {
    token: string;
}

const accountAPI = {
    /**
     * 验证码发送接口
     */
    sendYzm: {
        url: `/tenant/account/sendyzm`,
        name: '发送验证码',
        post: async function (data: SendYzmParams = { type: 'sms' }) {
            return await http.post(this.url, data);
        }
    },
    ping: {
        url: `/tenant/account/ping`,
        name: '检测用户在线状态',
        get: async function (data: any = {}) {
            return await http.post(this.url, data, { silent: true });
        }
    },
    /**
     * 账号登录接口
     */
    login: {
        url: `/tenant/account/login`,
        name: '登录获取ACCESS_TOKEN',
        post: async function (data: any) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 退出登录接口
     */
    logout: {
        url: `/tenant/account/logout`,
        name: '退出登录',
        post: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 获取用户菜单接口
     */
    menu: {
        url: `/tenant/account/menu`,
        name: '获取用户菜单',
        get: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 获取用户基本信息接口
     */
    info: {
        url: `/tenant/account/info`,
        name: '验证用户是否登录（也可用作获取用户基本信息）',
        get: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 更新用户基本信息接口
     */
    edit: {
        url: `/tenant/account/edit`,
        name: '更新用户基本信息',
        post: async function (data: any) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 更新用户资料接口（edit的别名，用于兼容性）
     */
    updateProfile: {
        url: `/tenant/account/updateProfile`,
        name: '更新用户资料',
        post: async function (data: any) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 获取用户操作日志接口
     */
    logs: {
        url: `/tenant/account/logs`,
        name: '操作日志',
        get: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 强制修改密码是不需要传入原密码
     * 主动修改密码需要验证原密码
     */
    changePassword: {
        url: `/tenant/account/changePassword`,
        name: '重置密码',
        post: async function (data: ChangePasswordParams) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 服务端锁屏接口
     */
    lockscreen: {
        url: `/tenant/account/lockscreen`,
        name: '锁定屏幕',
        post: async function (data: LockscreenParams) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 锁屏解锁接口
     */
    unLockscreen: {
        url: `/tenant/account/unLockscreen`,
        name: '解锁屏幕',
        post: async function (data: LockscreenParams) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 刷新用户token接口
     */
    refreshToken: {
        url: `/tenant/account/refreshToken`,
        name: '刷新用户token',
        post: async function (data: RefreshTokenParams) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 验证token合法性接口
     */
    verifyToken: {
        url: `/tenant/account/verifyToken`,
        name: '验证token合法性',
        post: async function (data: VerifyTokenParams) {
            return await http.post(this.url, data);
        }
    }
};

export default accountAPI;
