import http from '@/utils/request';

const notificationAPI = {
    /**
     * 公告通知列表接口
     */
    list: {
        url: `/tenant/notification/list`,  // 注意：heli分支使用 /tenant 前缀
        name: '公告通知列表',
        get: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 标记已读接口
     */
    status: {
        url: `/tenant/notification/status`,
        name: '标记已读',
        post: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 互动接口
     */
    interact: {
        url: `/tenant/notification/interact`,
        name: '互动（点赞/踩/取消）',
        post: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 获取未读消息数
     */
    unreadCount: {
        url: `/tenant/notification/unreadCount`,
        name: '获取未读消息数',
        get: async function () {
            return await http.post(this.url);
        }
    },
    /**
     * 获取消息详情
     */
    detail: {
        url: `/tenant/notification/detail`,
        name: '获取消息详情',
        get: async function (id: string | number) {
            return await http.post(`${this.url}/${id}`);
        }
    },
    /**
     * 全部标记已读
     */
    markAllRead: {
        url: `/tenant/notification/markAllRead`,
        name: '全部标记已读',
        post: async function () {
            return await http.post(this.url);
        }
    },
    /**
     * 获取消息分类列表
     */
    categories: {
        url: `/tenant/notification/categories`,
        name: '获取消息分类',
        get: async function () {
            return await http.post(this.url);
        }
    },
    /**
     * 发送消息
     */
    send: {
        url: `/tenant/notification/send`,
        name: '发送消息',
        post: async function (data: any) {
            const { title, content, type, receive_range, target_ids } = data;
            return await http.post(this.url, {
                title,
                content,
                type: Number(type),
                receive_range: Number(receive_range),
                target_ids: target_ids
            });
        }
    },
    /**
     * 获取可选接收者列表
     */
    receivers: {
        url: `/tenant/notification/receivers`,
        name: '获取接收者列表',
        get: async function () {
            return await http.get(this.url);
        }
    },
    /**
     * 获取可选部门列表
     */
    departments: {
        url: `/tenant/notification/departments`,
        name: '获取部门列表',
        get: async function () {
            return await http.post(this.url);
        }
    },
    /**
     * 获取已发送消息列表
     */
    sentList: {
        url: `/tenant/notification/sentList`,
        name: '已发送消息列表',
        get: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 删除消息接口
     */
    delete: {
        url: `/tenant/notification/delete`,
        name: '删除消息',
        post: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 批量标记消息状态
     */
    batchStatus: {
        url: `/tenant/notification/batchStatus`,
        name: '批量标记消息状态',
        post: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 批量消息软删除
     */
    batchDelete: {
        url: `/tenant/notification/batchDelete`,
        name: '批量消息软删除',
        post: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 获取消息互动状态
     */
    interactStatus: {
        url: `/tenant/notification/interactStatus`,
        name: '获取消息互动状态',
        get: async function (id: string | number) {
            return await http.post(this.url, { id });
        }
    }
};

export default notificationAPI;
