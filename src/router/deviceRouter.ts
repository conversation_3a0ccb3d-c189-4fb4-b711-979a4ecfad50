import type { RouteRecordRaw } from 'vue-router';

// 设备管理路由配置
const routes: RouteRecordRaw[] = [
    {
        path: '/tenant/handle/device',
        component: () => import('@/views/tenant/handle/device.vue'),
        meta: {
            title: '设备管理'
        },
        children: [
            {
                path: ':id',
                component: () => import('@/views/tenant/handle/components/well/index.vue'),
                meta: {
                    title: '设备监控'
                }
            },
            {
                path: ':id/history',
                component: () => import('@/views/tenant/handle/components/HistoryData.vue'),
                meta: {
                    title: '历史数据'
                }
            },
            {
                path: ':id/layer/:layer',
                component: () => import('@/views/tenant/handle/components/well/wellDetail.vue'),
                meta: {
                    title: '分层详情'
                }
            }
        ]
    }
];

export default routes;
