import mqttMessageModel from '@/model/mqttMessageModel';
import loggerFactory from '@/utils/logger';
import mqttConfig from '@/config/mqtt';
import type { IMqttMessageService, PersistedMessage } from './types';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('MQTTMsgService');

// 消息缓存类型
interface MessageCache {
    received: PersistedMessage[];
    sent: PersistedMessage[];
}

/**
 * MQTT消息服务
 * 负责管理MQTT消息的存储和检索
 * 全局持久化MQTT消息，无论mqtt-test组件是否打开
 */
class MqttMessageService implements IMqttMessageService {
    private initialized = false;
    private messageCache: MessageCache = {
        received: [],
        sent: []
    };

    constructor() {
        // 检查是否启用消息持久化
        if (mqttConfig.messagePersistence.enabled) {
            // 立即初始化，确保全局消息捕获
            this.init().then(() => {
                // 绑定消息处理事件
                window.addEventListener('mqtt-message-received', this.handleMessageReceived.bind(this));
                window.addEventListener('mqtt-message-sent', this.handleMessageSent.bind(this));
            });
        } else {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('MQTT消息持久化已禁用');
            }
        }
    }

    /**
     * 初始化服务
     * 确保数据库初始化并预加载消息
     */
    async init(): Promise<boolean> {
        if (this.initialized) return true;

        // 检查是否启用消息持久化
        if (!mqttConfig.messagePersistence.enabled) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('MQTT消息持久化已禁用，跳过初始化');
            }
            return false;
        }

        if (mqttConfig.messagePersistence.enableLogging) {
            logger.group('初始化MQTT消息服务');
            logger.info('开始初始化MQTT消息服务');
        }

        try {
            // 数据库已经在模型构造函数中初始化，这里只需等待初始化完成
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('等待数据库初始化完成');
            }
            await (mqttMessageModel as any).initPromise;
            this.initialized = true;

            // 预加载消息到缓存
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('开始预加载消息到缓存');
            }
            await this.preloadMessages();

            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('MQTT消息服务初始化成功，消息持久化已启用');
                logger.groupEnd();
            }
            return true;
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.error('MQTT消息服务初始化失败:', error);
                logger.groupEnd();
            }
            return false;
        }
    }

    /**
     * 预加载消息到缓存
     * 确保即使在组件未加载时也能访问历史消息
     */
    private async preloadMessages(): Promise<void> {
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.group('预加载MQTT消息');
        }

        try {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('加载接收消息');
            }
            this.messageCache.received = await (mqttMessageModel as any).getReceivedMessages();
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info(`已加载 ${this.messageCache.received.length} 条接收消息`);
            }

            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('加载发送消息');
            }
            this.messageCache.sent = await (mqttMessageModel as any).getSentMessages();
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info(`已加载 ${this.messageCache.sent.length} 条发送消息`);
            }

            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('预加载MQTT消息完成');
                logger.groupEnd();
            }
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.error('预加载MQTT消息失败:', error);
                logger.groupEnd();
            }
        }
    }

    /**
     * 处理接收到的消息
     */
    private async handleMessageReceived(event: CustomEvent): Promise<void> {
        // 检查是否启用消息持久化
        if (!mqttConfig.messagePersistence.enabled) {
            return;
        }

        if (!this.initialized) {
            // 如果服务尚未初始化，先初始化
            await this.init();
        }

        try {
            const message = event.detail;
            // 保存到数据库
            await (mqttMessageModel as any).addReceivedMessage(message);
            // 更新缓存
            this.messageCache.received = await (mqttMessageModel as any).getReceivedMessages();

            // 使用debug级别记录日志，避免日志过多
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.debug(`已保存接收消息: ${message.topic}`);
            }
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.error('保存接收消息失败:', error);
            }
        }
    }

    /**
     * 处理发送的消息
     */
    private async handleMessageSent(event: CustomEvent): Promise<void> {
        // 检查是否启用消息持久化
        if (!mqttConfig.messagePersistence.enabled) {
            return;
        }

        if (!this.initialized) {
            // 如果服务尚未初始化，先初始化
            await this.init();
        }

        try {
            const message = event.detail;
            // 保存到数据库
            await (mqttMessageModel as any).addSentMessage(message);
            // 更新缓存
            this.messageCache.sent = await (mqttMessageModel as any).getSentMessages();

            // 使用debug级别记录日志，避免日志过多
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.debug(`已保存发送消息: ${message.topic}`);
            }
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.error('保存发送消息失败:', error);
            }
        }
    }

    /**
     * 保存消息
     */
    async saveMessage(message: PersistedMessage): Promise<boolean> {
        if (!mqttConfig.messagePersistence.enabled) {
            return false;
        }

        if (!this.initialized) await this.init();

        try {
            if (message.direction === 'received') {
                await (mqttMessageModel as any).addReceivedMessage(message);
            } else {
                await (mqttMessageModel as any).addSentMessage(message);
            }
            return true;
        } catch (error) {
            logger.error('保存消息失败:', error);
            return false;
        }
    }

    /**
     * 获取接收的消息列表
     * @returns Promise<Array>
     */
    async getReceivedMessages(): Promise<PersistedMessage[]> {
        // 如果未启用消息持久化，返回空数组
        if (!mqttConfig.messagePersistence.enabled) {
            return [];
        }

        if (!this.initialized) await this.init();

        // 刷新缓存并返回最新数据
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.debug('获取接收消息列表');
        }
        this.messageCache.received = await (mqttMessageModel as any).getReceivedMessages();
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.debug(`获取到 ${this.messageCache.received.length} 条接收消息`);
        }

        return this.messageCache.received;
    }

    /**
     * 获取发送的消息列表
     * @returns Promise<Array>
     */
    async getSentMessages(): Promise<PersistedMessage[]> {
        // 如果未启用消息持久化，返回空数组
        if (!mqttConfig.messagePersistence.enabled) {
            return [];
        }

        if (!this.initialized) await this.init();

        // 刷新缓存并返回最新数据
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.debug('获取发送消息列表');
        }
        this.messageCache.sent = await (mqttMessageModel as any).getSentMessages();
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.debug(`获取到 ${this.messageCache.sent.length} 条发送消息`);
        }

        return this.messageCache.sent;
    }

    /**
     * 获取消息列表
     */
    async getMessages(limit?: number, offset?: number): Promise<PersistedMessage[]> {
        if (!mqttConfig.messagePersistence.enabled) {
            return [];
        }

        if (!this.initialized) await this.init();

        try {
            const received = await (mqttMessageModel as any).getReceivedMessages();
            const sent = await (mqttMessageModel as any).getSentMessages();

            // 合并并按时间排序
            const allMessages = [...received, ...sent].sort((a, b) => b.time - a.time);

            if (limit) {
                const start = offset || 0;
                return allMessages.slice(start, start + limit);
            }

            return allMessages;
        } catch (error) {
            logger.error('获取消息失败:', error);
            return [];
        }
    }

    /**
     * 清空消息
     */
    async clearMessages(): Promise<boolean> {
        if (!mqttConfig.messagePersistence.enabled) {
            return false;
        }

        if (!this.initialized) await this.init();

        try {
            await (mqttMessageModel as any).clearMessages();
            this.messageCache.received = [];
            this.messageCache.sent = [];
            return true;
        } catch (error) {
            logger.error('清空消息失败:', error);
            return false;
        }
    }

    /**
     * 获取消息数量
     */
    async getMessageCount(): Promise<number> {
        if (!mqttConfig.messagePersistence.enabled) {
            return 0;
        }

        if (!this.initialized) await this.init();

        try {
            const received = await (mqttMessageModel as any).getReceivedMessages();
            const sent = await (mqttMessageModel as any).getSentMessages();
            return received.length + sent.length;
        } catch (error) {
            logger.error('获取消息数量失败:', error);
            return 0;
        }
    }

    /**
     * 销毁服务
     */
    destroy(): void {
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.group('销毁MQTT消息服务');
            logger.info('移除事件监听器');
        }

        window.removeEventListener('mqtt-message-received', this.handleMessageReceived);
        window.removeEventListener('mqtt-message-sent', this.handleMessageSent);

        // 关闭数据库连接
        if (this.initialized && (mqttMessageModel as any).close) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('关闭数据库连接');
            }
            (mqttMessageModel as any).close();
            this.initialized = false;
        }

        if (mqttConfig.messagePersistence.enableLogging) {
            logger.info('MQTT消息服务已销毁');
            logger.groupEnd();
        }
    }

    /**
     * 检查是否已初始化
     */
    isInitialized(): boolean {
        return this.initialized;
    }
}

const mqttMessageService = new MqttMessageService();
export default mqttMessageService;
