/**
 * 服务层类型定义
 */

import type { MqttClient, IClientOptions } from 'mqtt';

// MQTT连接配置类型
export interface MqttConnectionConfig {
    url: string;
    clientId: string;
    username: string;
    password: string;
}

// MQTT订阅信息类型
export interface MqttSubscription {
    topic: string;
    callback: (topic: string, message: Buffer) => void;
    qos: 0 | 1 | 2;
}

// MQTT发布选项类型
export interface MqttPublishOptions {
    qos?: 0 | 1 | 2;
    retain?: boolean;
    dup?: boolean;
}

// MQTT状态监听器类型
export type MqttStateListener = (connected: boolean, retrying: boolean) => void;

// MQTT消息类型
export interface MqttMessage {
    topic: string;
    content: any;
    qos: 0 | 1 | 2;
    time: number;
    retain?: boolean;
}

// 设备状态类型
export interface DeviceStatus {
    deviceId: string;
    status: 'online' | 'offline' | 'unknown';
    lastHeartbeat: number;
    tenantCode: string;
    clientType?: string;
    metadata?: Record<string, any>;
}

// 设备在线服务配置类型
export interface DeviceOnlineConfig {
    heartbeatInterval: number;
    offlineThreshold: number;
    cleanupInterval: number;
    maxDevices: number;
}

// 历史数据查询参数类型
export interface HistoryDataQuery {
    deviceId?: string;
    tenantCode?: string;
    startTime: number;
    endTime: number;
    dataType?: string;
    limit?: number;
    offset?: number;
}

// 历史数据项类型
export interface HistoryDataItem {
    id: string;
    deviceId: string;
    tenantCode: string;
    dataType: string;
    value: any;
    timestamp: number;
    metadata?: Record<string, any>;
}

// 历史数据响应类型
export interface HistoryDataResponse {
    items: HistoryDataItem[];
    total: number;
    hasMore: boolean;
}

// MQTT消息持久化配置类型
export interface MessagePersistenceConfig {
    enabled: boolean;
    maxMessages: number;
    enableLogging: boolean;
    dbName: string;
    storeName: string;
}

// 持久化消息类型
export interface PersistedMessage {
    id?: number;
    topic: string;
    content: string;
    qos: 0 | 1 | 2;
    time: number;
    direction: 'sent' | 'received';
    retain?: boolean;
}

// 服务基类接口
export interface BaseService {
    init(): Promise<boolean>;
    destroy(): void;
    isInitialized(): boolean;
}

// MQTT服务接口
export interface IMqttService extends BaseService {
    // 连接管理
    connect(username: string, password: string): Promise<boolean>;
    disconnect(silent?: boolean): void;
    
    // 状态管理
    readonly isConnected: boolean;
    readonly isRetrying: boolean;
    addStateListener(listener: MqttStateListener): void;
    removeStateListener(listener: MqttStateListener): void;
    
    // 订阅管理
    subscribe(topic: string, callback: (topic: string, message: Buffer) => void, qos?: 0 | 1 | 2, silent?: boolean): Promise<boolean>;
    unsubscribe(topic: string, silent?: boolean): Promise<boolean>;
    getSubscriptions(): MqttSubscription[];
    
    // 消息发布
    publish(topic: string, payload: string | object, options?: MqttPublishOptions, silent?: boolean): Promise<boolean>;
    
    // 自动订阅
    autoSubscribeTopics(): Promise<Array<{topic: string, success: boolean}>>;
}

// 设备在线服务接口
export interface IDeviceOnlineService extends BaseService {
    updateDeviceStatus(deviceId: string, status: DeviceStatus): void;
    getDeviceStatus(deviceId: string): DeviceStatus | null;
    getAllDeviceStatuses(): DeviceStatus[];
    removeDevice(deviceId: string): void;
    getOnlineDeviceCount(): number;
    getOfflineDeviceCount(): number;
}

// 历史数据服务接口
export interface IHistoryDataService extends BaseService {
    queryHistoryData(query: HistoryDataQuery): Promise<HistoryDataResponse>;
    addHistoryData(data: HistoryDataItem): Promise<boolean>;
    deleteHistoryData(id: string): Promise<boolean>;
    clearHistoryData(tenantCode?: string): Promise<boolean>;
}

// MQTT消息服务接口
export interface IMqttMessageService extends BaseService {
    saveMessage(message: PersistedMessage): Promise<boolean>;
    getMessages(limit?: number, offset?: number): Promise<PersistedMessage[]>;
    clearMessages(): Promise<boolean>;
    getMessageCount(): Promise<number>;
}
