import type { ZhCnLocale } from './zh-cn';

// 英文语言包（复用中文语言包的类型结构）
const enLocale: ZhCnLocale = {
    common: {
        confirm: 'Confirm',
        cancel: 'Cancel',
        save: 'Save',
        delete: 'Delete',
        edit: 'Edit',
        add: 'Add',
        search: 'Search',
        reset: 'Reset',
        submit: 'Submit',
        back: 'Back',
        close: 'Close',
        loading: 'Loading...',
        noData: 'No Data',
        operation: 'Operation',
        status: 'Status',
        createTime: 'Create Time',
        updateTime: 'Update Time',
        remark: 'Remark',
        success: 'Success',
        error: 'Error',
        warning: 'Warning',
        info: 'Info'
    },
    login: {
        title: 'User Login',
        username: 'Userna<PERSON>',
        password: 'Password',
        captcha: 'Captcha',
        remember: 'Remember Me',
        login: 'Login',
        register: 'Register',
        forgetPassword: 'Forget Password',
        loginSuccess: 'Login Success',
        loginFailed: 'Login Failed',
        usernameRequired: 'Please enter username',
        passwordRequired: 'Please enter password',
        captchaRequired: 'Please enter captcha'
    },
    menu: {
        dashboard: 'Dashboard',
        system: 'System',
        user: 'User',
        role: 'Role',
        permission: 'Permission',
        tenant: 'Tenant',
        device: 'Device',
        monitor: 'Monitor',
        log: 'Log',
        setting: 'Setting'
    },
    tenant: {
        list: 'Tenant List',
        add: 'Add Tenant',
        edit: 'Edit Tenant',
        delete: 'Delete Tenant',
        name: 'Tenant Name',
        code: 'Tenant Code',
        status: 'Status',
        enable: 'Enable',
        disable: 'Disable',
        createTime: 'Create Time',
        remark: 'Remark',
        manage: 'Tenant Management',
        device: 'Device Management',
        user: 'User Management',
        config: 'Config Management'
    },
    device: {
        list: 'Device List',
        add: 'Add Device',
        edit: 'Edit Device',
        delete: 'Delete Device',
        name: 'Device Name',
        code: 'Device Code',
        type: 'Device Type',
        status: 'Device Status',
        online: 'Online',
        offline: 'Offline',
        location: 'Device Location',
        monitor: 'Device Monitor',
        control: 'Device Control',
        history: 'History Data',
        realtime: 'Realtime Data',
        alarm: 'Alarm Info'
    },
    notification: {
        title: 'Notification',
        content: 'Content',
        type: 'Type',
        status: 'Status',
        read: 'Read',
        unread: 'Unread',
        markRead: 'Mark Read',
        markAllRead: 'Mark All Read',
        delete: 'Delete Message',
        deleteAll: 'Clear All',
        system: 'System Message',
        notice: 'Notice',
        message: 'Message',
        all: 'All Messages',
        sent: 'Sent'
    }
};

export default enLocale;
