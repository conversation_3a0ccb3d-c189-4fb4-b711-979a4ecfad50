import { createI18n } from 'vue-i18n';
import type { Language } from 'element-plus/es/locale';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import enCn from 'element-plus/es/locale/lang/en';
import tool from '@/utils/tool';
import type { AppConfig } from '@/config';
import config from '@/config';
import type { StorageConfig } from '@/config/storage';
import storageConfig from '@/config/storage';

// 支持的语言类型
type SupportedLocale = 'zh-cn' | 'en';

// 本地化消息类型
interface LocaleMessage {
    [key: string]: any;
}

// 消息结构类型
type Messages = {
    [K in SupportedLocale]: {
        el: Language;
    } & LocaleMessage;
};

// 导入语言包
import zhCnLocale from './lang/zh-cn';
import enLocale from './lang/en';

const messages: Messages = {
    'zh-cn': {
        ...zhCnLocale,
        el: zhCn
    },
    'en': {
        ...enLocale,
        el: enCn
    }
};

const i18n = createI18n({
    locale: (tool.data.get(storageConfig.vars.appLang) || config.APP_LANG) as SupportedLocale,
    fallbackLocale: 'zh-cn' as SupportedLocale,
    globalInjection: true,
    messages: messages as any
});

export default i18n;
