import type { DirectiveBinding } from 'vue';
import tool from '@/utils/tool';

// 时间工具类型
interface TimeUtils {
    getUnix(): number;
    getTodayUnix(): number;
    getYearUnix(): number;
    getLastDate(time: number | Date): string;
    getFormateTime(timestamp: number | Date): string;
}

// 扩展HTMLElement类型以包含自定义属性
interface TimeElement extends HTMLElement {
    __timeout__?: NodeJS.Timeout;
}

const Time: TimeUtils = {
    // 获取当前时间戳
    getUnix: function (): number {
        const date = new Date();
        return date.getTime();
    },
    // 获取今天0点0分0秒的时间戳
    getTodayUnix: function (): number {
        const date = new Date();
        date.setHours(0);
        date.setMinutes(0);
        date.setSeconds(0);
        date.setMilliseconds(0);
        return date.getTime();
    },
    // 获取今年1月1日0点0秒的时间戳
    getYearUnix: function (): number {
        const date = new Date();
        date.setMonth(0);
        date.setDate(1);
        date.setHours(0);
        date.setMinutes(0);
        date.setSeconds(0);
        date.setMilliseconds(0);
        return date.getTime();
    },
    // 获取标准年月日
    getLastDate: function (time: number | Date): string {
        const date = new Date(time);
        const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
        const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
        return date.getFullYear() + '-' + month + '-' + day;
    },
    // 转换时间
    getFormateTime: function (timestamp: number | Date): string {
        const timestampDate = new Date(timestamp);
        const now = this.getUnix();
        const today = this.getTodayUnix();
        // const year = this.getYearUnix();
        const timer = (now - timestampDate.getTime()) / 1000;
        let tip = '';

        if (timer <= 0) {
            tip = '刚刚';
        } else if (Math.floor(timer / 60) <= 0) {
            tip = '刚刚';
        } else if (timer < 3600) {
            tip = Math.floor(timer / 60) + '分钟前';
        } else if (timer >= 3600 && timestampDate.getTime() - today >= 0) {
            tip = Math.floor(timer / 3600) + '小时前';
        } else if (timer / 86400 <= 31) {
            tip = Math.ceil(timer / 86400) + '天前';
        } else {
            tip = this.getLastDate(timestampDate);
        }
        return tip;
    }
};

export default (el: TimeElement, binding: DirectiveBinding<number | string>) => {
    let { value, modifiers } = binding;
    if (!value) {
        return false;
    }
    let numValue = typeof value === 'string' ? parseInt(value) : value;
    if (numValue.toString().length === 10) {
        numValue = numValue * 1000;
    }
    if (modifiers.tip) {
        el.innerHTML = Time.getFormateTime(numValue);
        el.__timeout__ = setInterval(() => {
            el.innerHTML = Time.getFormateTime(numValue);
        }, 60000);
    } else {
        const format = el.getAttribute('format') || undefined;
        el.innerHTML = tool.dateFormat(numValue, format);
    }
};
