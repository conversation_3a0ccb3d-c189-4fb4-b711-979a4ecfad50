import type { DirectiveBinding } from 'vue';
import { debounce, throttle } from '@/utils/throttleDebounce';

// 防抖指令参数类型
export interface DebounceOptions {
    fn: (...args: any[]) => any;
    delay?: number;
    immediate?: boolean;
}

// 节流指令参数类型
export interface ThrottleOptions {
    fn: (...args: any[]) => any;
    delay?: number;
    options?: any;
}

// 扩展HTMLElement类型以包含自定义属性
interface ThrottleDebounceElement extends HTMLElement {
    handler: (...args: any[]) => any;
}

/**
 * 防抖指令
 * 使用方式：
 * <!-- 防抖指令 -->
 * <button v-debounce="{ fn: handleClick, delay: 500 }">点击</button>
 *
 * <!-- 节流指令 -->
 * <div v-throttle="{ fn: handleScroll, delay: 200 }">滚动</div>
 *
 * <!-- 直接使用工具函数 -->
 * <script>
 * import { debounce, throttle } from '@/utils/throttleDebounce';
 *
 * export default {
 *     created() {
 *         this.debouncedFn = debounce(this.originalFn, 300);
 *         this.throttledFn = throttle(this.originalFn, 300);
 *
 *         // 或者使用全局方法
 *         this.debouncedFn = this.$debounce(this.originalFn, 300);
 *         this.throttledFn = this.$throttle(this.originalFn, 300);
 *     }
 * }
 * </script>
 */
export const vDebounce = {
    mounted(el: ThrottleDebounceElement, binding: DirectiveBinding<DebounceOptions>) {
        const { fn, delay = 300, immediate = false } = binding.value;
        el.handler = debounce(fn, delay, immediate);
        el.addEventListener('click', el.handler);
    },
    unmounted(el: ThrottleDebounceElement) {
        el.removeEventListener('click', el.handler);
    }
};

/**
 * 节流指令
 * 使用方式：v-throttle="{ fn: handleScroll, delay: 200 }"
 */
export const vThrottle = {
    mounted(el: ThrottleDebounceElement, binding: DirectiveBinding<ThrottleOptions>) {
        const { fn, delay = 300, options = {} } = binding.value;
        el.handler = throttle(fn, delay, options);
        el.addEventListener('click', el.handler);
    },
    unmounted(el: ThrottleDebounceElement) {
        el.removeEventListener('click', el.handler);
    }
};

export default {
    debounce: vDebounce,
    throttle: vThrottle
};
