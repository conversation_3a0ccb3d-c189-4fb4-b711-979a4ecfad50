/**
 * 将时间戳格式化为指定格式的日期字符串
 * @param {number} timestamp - 时间戳（自动检测秒级或毫秒级）
 * @param {string} [format='YYYY/MM/DD HH:mm:ss'] - 输出格式
 * @returns {string} 格式化后的日期字符串
 */
export function formatTimestamp(timestamp, format = 'YYYY/MM/DD HH:mm:ss') {
    if (!timestamp || timestamp === 0) return '';

    // 自动检测时间戳格式
    // 如果时间戳长度为10位，认为是秒级时间戳，需要转换为毫秒
    // 如果时间戳长度为13位，认为是毫秒级时间戳
    let milliseconds = timestamp;
    if (timestamp.toString().length === 10) {
        milliseconds = timestamp * 1000;
    }

    // 验证时间戳是否合理（1970年之后的时间）
    if (milliseconds < 0 || milliseconds < 946684800000) { // 2000年1月1日之前的时间可能有问题
        console.warn('时间戳可能有误:', timestamp, '转换后:', milliseconds);
        return '时间格式错误';
    }

    const date = new Date(milliseconds);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
        console.warn('无效的时间戳:', timestamp);
        return '无效时间';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}
