import { openDB, deleteDB } from 'idb';
import storage from '@/config/storage';
import loggerFactory from '@/utils/logger';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('IndexedDB');

// 数据库配置
const DB_NAME = storage.indexedDB.dbName;
const DB_VERSION = storage.indexedDB.dbVersion;

// 数据库连接
/** @type {import('idb').IDBPDatabase | null} */
let dbInstance = null;

// 初始化 Promise
/** @type {Promise<import('idb').IDBPDatabase> | null} */
let initPromise = null;

/**
 * 初始化数据库
 * @returns {Promise<import('idb').IDBPDatabase>} 数据库实例
 */
async function initDatabase() {
    if (dbInstance) {
        return dbInstance;
    }

    if (initPromise) {
        return initPromise;
    }

    logger.group(`初始化数据库 ${DB_NAME}`);
    logger.info(`开始初始化数据库 ${DB_NAME}`);

    initPromise = openDB(DB_NAME, DB_VERSION, {
        upgrade(database, oldVersion, newVersion) {
            logger.group(`升级数据库 ${DB_NAME}`);
            logger.info(`升级数据库 ${DB_NAME}: ${oldVersion} -> ${newVersion}`);

            // 如果数据库版本号不一致，删除旧数据库
            const STORES = storage.indexedDB.storeConfigs;

            // 创建所有存储对象
            Object.values(STORES).forEach(store => {
                if (!database.objectStoreNames.contains(store.name)) {
                    logger.info(`创建存储对象: ${store.name}`);
                    const objectStore = database.createObjectStore(store.name, {
                        keyPath: store.keyPath,
                        autoIncrement: store.autoIncrement || false
                    });

                    // 创建索引
                    if (store.indexes) {
                        logger.group(`创建索引 (${store.name})`);
                        store.indexes.forEach(index => {
                            logger.info(`创建索引: ${index.name}`);
                            objectStore.createIndex(index.name, index.keyPath, index.options || {});
                        });
                        logger.groupEnd();
                    }
                }
            });
            logger.groupEnd();
        },
        blocked() {
            logger.warn(`数据库 ${DB_NAME} 被阻塞`);
        },
        blocking() {
            logger.warn(`数据库 ${DB_NAME} 版本变更被阻塞`);
            closeDatabase();
        },
        terminated() {
            logger.warn(`数据库 ${DB_NAME} 连接意外终止`);
            dbInstance = null;
            initPromise = null;
        }
    });

    try {
        dbInstance = await initPromise;
        logger.info(`数据库 ${DB_NAME} 初始化成功`);
        logger.groupEnd(); // 结束初始化分组
        return dbInstance;
    } catch (error) {
        logger.error(`初始化数据库 ${DB_NAME} 失败:`, error);
        logger.groupEnd(); // 结束初始化分组
        dbInstance = null;
        initPromise = null;
        throw error;
    }
}

/**
 * 获取数据库实例
 * @returns {Promise<import('idb').IDBPDatabase>} 数据库实例
 */
export async function getDatabase() {
    return dbInstance || initDatabase();
}

/**
 * 关闭数据库连接
 */
export function closeDatabase() {
    if (dbInstance) {
        logger.info(`关闭数据库 ${DB_NAME} 连接`);
        dbInstance.close();
        dbInstance = null;
    }
    initPromise = null;
}

/**
 * 删除数据库
 * @returns {Promise<void>}
 */
export async function deleteDatabase() {
    try {
        // 先关闭连接
        closeDatabase();

        logger.group(`删除数据库 ${DB_NAME}`);
        logger.info(`开始删除数据库 ${DB_NAME}`);
        await deleteDB(DB_NAME);

        logger.info(`数据库 ${DB_NAME} 删除成功`);
        logger.groupEnd();
    } catch (error) {
        logger.error(`删除数据库 ${DB_NAME} 失败:`, error);
        throw error;
    }
}

// 导出数据库操作方法
export const db = {
    /**
     * 获取单个数据
     * @param {String} storeName 存储对象名称
     * @param {*} key 键
     * @returns {Promise<*>} 数据
     */
    async get(storeName, key) {
        try {
            /** @type {import('idb').IDBPDatabase} */
            const database = await getDatabase();
            return database.get(storeName, key);
        } catch (error) {
            logger.error(`获取数据失败 [${storeName}][${key}]:`, error);
            throw error;
        }
    },

    /**
     * 获取所有数据
     * @param {String} storeName 存储对象名称
     * @returns {Promise<Array>} 数据数组
     */
    async getAll(storeName) {
        try {
            /** @type {import('idb').IDBPDatabase} */
            const database = await getDatabase();
            return database.getAll(storeName);
        } catch (error) {
            logger.error(`获取所有数据失败 [${storeName}]:`, error);
            return [];
        }
    },

    /**
     * 添加数据
     * @param {String} storeName 存储对象名称
     * @param {*} data 数据
     * @returns {Promise<*>} 键
     */
    async add(storeName, data) {
        try {
            /** @type {import('idb').IDBPDatabase} */
            const database = await getDatabase();
            return database.add(storeName, data);
        } catch (error) {
            logger.error(`添加数据失败 [${storeName}]:`, error);
            throw error;
        }
    },

    /**
     * 更新数据
     * @param {String} storeName 存储对象名称
     * @param {*} data 数据
     * @returns {Promise<*>} 键
     */
    async put(storeName, data) {
        try {
            /** @type {import('idb').IDBPDatabase} */
            const database = await getDatabase();
            return database.put(storeName, data);
        } catch (error) {
            logger.error(`更新数据失败 [${storeName}]:`, error);
            throw error;
        }
    },

    /**
     * 删除数据
     * @param {String} storeName 存储对象名称
     * @param {*} key 键
     * @returns {Promise<void>}
     */
    async delete(storeName, key) {
        try {
            /** @type {import('idb').IDBPDatabase} */
            const database = await getDatabase();
            return database.delete(storeName, key);
        } catch (error) {
            logger.error(`删除数据失败 [${storeName}][${key}]:`, error);
            throw error;
        }
    },

    /**
     * 清空存储对象
     * @param {String} storeName 存储对象名称
     * @returns {Promise<void>}
     */
    async clear(storeName) {
        try {
            /** @type {import('idb').IDBPDatabase} */
            const database = await getDatabase();
            return database.clear(storeName);
        } catch (error) {
            logger.error(`清空数据失败 [${storeName}]:`, error);
            throw error;
        }
    },

    /**
     * 批量保存数据（先清空，再添加）
     * @param {String} storeName 存储对象名称
     * @param {Array} items 数据数组
     * @returns {Promise<void>}
     */
    async saveAll(storeName, items) {
        try {
            /** @type {import('idb').IDBPDatabase} */
            const database = await getDatabase();

            // 使用事务确保原子性
            const tx = database.transaction(storeName, 'readwrite');
            const store = tx.objectStore(storeName);

            // 先清空存储对象
            store.clear();

            // 添加所有数据
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                store.add({ ...item, order: i });
            }

            // 等待事务完成
            return tx.done;
        } catch (error) {
            logger.error(`批量保存数据失败 [${storeName}]:`, error);
            throw error;
        }
    }
};
