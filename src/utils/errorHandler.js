/**
 * 全局代码错误捕捉
 * 比如 null.length 就会被捕捉到
 */

// Vue 实例接口定义
interface VueInstance {
    $nextTick: (callback: () => void) => void;
    $notify: {
        error: (options: { title: string; message: any }) => void;
    };
}

// 错误类型映射
interface ErrorMap {
    [key: string]: string;
}

// 扩展 Error 接口以支持 status 属性
interface ExtendedError extends Error {
    status?: number;
}

/**
 * 全局错误处理器
 * @param error 错误对象
 * @param vm Vue实例
 * @returns 是否处理了错误
 */
const errorHandler = (error: ExtendedError, vm: VueInstance): boolean => {
    // 过滤HTTP请求错误
    if (error === undefined || error.status || error.status === 0) {
        return false;
    }

    const errorMap: ErrorMap = {
        InternalError: 'Javascript引擎内部错误',
        ReferenceError: '未找到对象',
        TypeError: '使用了错误的类型或对象',
        RangeError: '使用内置对象时，参数超范围',
        SyntaxError: '语法错误',
        EvalError: '错误的使用了Eval',
        URIError: 'URI错误'
    };

    const errorName = errorMap[error.name] || '未知错误';

    console.warn(`[FRAME error]: ${error}`);
    console.error(error);
    // throw error;

    vm.$nextTick(() => {
        vm.$notify.error({
            title: errorName,
            message: error
        });
    });

    return true;
};

export default errorHandler;
