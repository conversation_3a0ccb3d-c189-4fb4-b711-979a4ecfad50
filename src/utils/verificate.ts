/**
 * 表单验证工具
 * 提供常用的表单字段验证函数
 */

// Element Plus 表单验证规则类型
export interface FormRule {
    required?: boolean;
    message?: string;
    trigger?: string | string[];
    validator?: (rule: any, value: any, callback: (error?: Error) => void) => void;
}

// 验证回调函数类型
export type ValidateCallback = (error?: Error) => void;

/**
 * 验证手机号
 * @param rule 验证规则
 * @param value 输入值
 * @param callback 回调函数
 */
export function verifyPhone(rule: any, value: string, callback: ValidateCallback): void {
    const reg = /^[1][3456789][0-9]{9}$/;
    if (!reg.test(value)) {
        return callback(new Error('请输入正确的手机号码'));
    }
    callback();
}

/**
 * 验证车牌号码
 * @param rule 验证规则
 * @param value 输入值
 * @param callback 回调函数
 */
export function verifyCars(rule: any, value: string, callback: ValidateCallback): void {
    const reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$/;
    if (!reg.test(value)) {
        return callback(new Error('请输入正确的车牌号码'));
    }
    callback();
}
