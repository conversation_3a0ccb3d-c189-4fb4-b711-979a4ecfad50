/**
 * 日志工具类
 * 提供统一的日志打印接口，支持分组、折叠和不同级别的日志
 * 支持显示调用位置的行号
 */

// 日志级别枚举
export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    NONE = 4
}

// 日志器接口
export interface Logger {
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    group(label: string, collapsed?: boolean): void;
    groupEnd(): void;
    time(message: string, ...args: any[]): void;
    object(label: string, obj: any): void;
}

// 日志工厂接口
export interface LoggerFactory {
    createLogger(prefix: string): Logger;
    setLogLevel(level: LogLevel): void;
    getLogLevel(): LogLevel;
    LOG_LEVELS: typeof LogLevel;
}

// 是否启用日志
const isLogEnabled = process.env.NODE_ENV !== 'production';

// 当前日志级别，根据环境设置
let currentLogLevel = process.env.NODE_ENV === 'production' ? LogLevel.WARN : LogLevel.DEBUG;

/**
 * 获取调用堆栈信息
 * @returns 调用位置信息，格式为 "文件名:行号"
 */
function getCallerInfo(): string {
    try {
        // 创建一个错误对象以获取堆栈信息
        const err = new Error();
        const stack = err.stack?.split('\n');

        // 堆栈的第一行是 "Error"
        // 第二行是当前函数 getCallerInfo
        // 第三行是日志方法 (debug, info, warn, error)
        // 第四行是实际调用日志方法的位置
        if (stack && stack.length >= 4) {
            // 提取第四行的文件名和行号
            const callerLine = stack[3].trim();

            // 匹配文件路径和行号
            // 格式可能是 "at functionName (file:line:column)" 或 "at file:line:column"
            const match = callerLine.match(/\(([^)]+)\)/) || callerLine.match(/at\s+([^\s]+)/);

            if (match && match[1]) {
                const fileInfo = match[1];

                // 提取文件名和行号
                const fileMatch = fileInfo.match(/([^\/\\]+):(\d+):/);
                if (fileMatch) {
                    // 提取文件名，去掉可能的参数部分（如 file.js?t=1234567890）
                    let fileName = fileMatch[1];
                    // 如果文件名包含参数，只保留问号前面的部分
                    if (fileName.includes('?')) {
                        fileName = fileName.split('?')[0];
                    }
                    const lineNumber = fileMatch[2];
                    return `${fileName}:${lineNumber}`;
                }
            }
        }
    } catch (e) {
        // 如果解析堆栈失败，返回空字符串
        console.error('获取调用堆栈信息失败:', e);
    }

    return '';
}

// 日志分组计数器
let groupCounter = 0;

/**
 * 设置日志级别
 */
function setLogLevel(level: LogLevel): void {
    if (level >= LogLevel.DEBUG && level <= LogLevel.NONE) {
        currentLogLevel = level;
    }
}

/**
 * 获取当前日志级别
 */
function getLogLevel(): LogLevel {
    return currentLogLevel;
}

/**
 * 创建带有前缀的日志函数
 */
function createLogger(prefix: string): Logger {
    const logPrefix = prefix ? `[${prefix}]` : '';

    return {
        /**
         * 打印调试日志
         */
        debug(message: string, ...args: any[]): void {
            if (!isLogEnabled || currentLogLevel > LogLevel.DEBUG) return;
            const callerInfo = getCallerInfo();
            const logMessage = callerInfo ? `${logPrefix} [${callerInfo}] ${message}` : `${logPrefix} ${message}`;
            // 使用 console.log 代替 console.debug，确保在所有浏览器中都能显示
            console.debug('%c[DEBUG]', 'color:#ffa600;', logMessage, ...args);
        },

        /**
         * 打印信息日志
         */
        info(message: string, ...args: any[]): void {
            if (!isLogEnabled || currentLogLevel > LogLevel.INFO) return;
            const callerInfo = getCallerInfo();
            const logMessage = callerInfo ? `${logPrefix} [${callerInfo}] ${message}` : `${logPrefix} ${message}`;
            console.info(logMessage, ...args);
        },

        /**
         * 打印警告日志
         */
        warn(message: string, ...args: any[]): void {
            if (!isLogEnabled || currentLogLevel > LogLevel.WARN) return;
            const callerInfo = getCallerInfo();
            const logMessage = callerInfo ? `${logPrefix} [${callerInfo}] ${message}` : `${logPrefix} ${message}`;
            console.warn(logMessage, ...args);
        },

        /**
         * 打印错误日志
         */
        error(message: string, ...args: any[]): void {
            if (!isLogEnabled || currentLogLevel > LogLevel.ERROR) return;
            const callerInfo = getCallerInfo();
            const logMessage = callerInfo ? `${logPrefix} [${callerInfo}] ${message}` : `${logPrefix} ${message}`;
            console.error(logMessage, ...args);
        },

        /**
         * 开始一个折叠的日志组
         */
        group(label: string, collapsed: boolean = true): void {
            if (!isLogEnabled) return;

            groupCounter++;

            // 根据日志级别决定是否显示组标签
            const showLabel = currentLogLevel <= LogLevel.INFO;

            // 获取调用位置信息
            const callerInfo = getCallerInfo();
            const groupLabel = callerInfo && showLabel
                ? `${logPrefix} [${callerInfo}] ${label}`
                : showLabel ? `${logPrefix} ${label}` : '';

            if (collapsed) {
                console.groupCollapsed(groupLabel);
            } else {
                console.group(groupLabel);
            }
        },

        /**
         * 结束当前日志组
         */
        groupEnd(): void {
            if (!isLogEnabled || groupCounter <= 0) return;

            console.groupEnd();
            groupCounter--;
        },

        /**
         * 打印带有时间戳的日志
         */
        time(message: string, ...args: any[]): void {
            if (!isLogEnabled || currentLogLevel > LogLevel.INFO) return;

            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN', { hour12: false }) + '.' + now.getMilliseconds().toString().padStart(3, '0');

            // 获取调用位置信息
            const callerInfo = getCallerInfo();
            const logMessage = callerInfo
                ? `${logPrefix} [${timeStr}] [${callerInfo}] ${message}`
                : `${logPrefix} [${timeStr}] ${message}`;

            console.info(logMessage, ...args);
        },

        /**
         * 打印对象，自动折叠
         */
        object(label: string, obj: any): void {
            if (!isLogEnabled || currentLogLevel > LogLevel.DEBUG) return;

            // 获取调用位置信息并添加到标签中
            const callerInfo = getCallerInfo();
            const groupLabel = callerInfo ? `${label} [${callerInfo}]` : label;

            // 使用自定义前缀
            console.log('%c[DEBUG]', 'color: #6c757d;', `${logPrefix} 对象: ${groupLabel}`);
            console.dir(obj);
        }
    };
}

// 创建默认日志记录器
const defaultLogger = createLogger('App');

// 导出日志工具
const loggerFactory: LoggerFactory & Logger = {
    createLogger,
    setLogLevel,
    getLogLevel,
    LOG_LEVELS: LogLevel,
    ...defaultLogger
};

export default loggerFactory;
