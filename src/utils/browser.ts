// 浏览器信息接口
export interface BrowserInfo {
    type: 'Chrome' | 'Firefox' | 'Safari' | 'Edg' | 'Opera' | 'IE' | 'unknown';
    version: string;
    userAgent: string;
}

// 浏览器最低版本要求
interface MinVersionMap {
    Chrome: number;
    Firefox: number;
    Safari: number;
    Edg: number;
    IE: number;
}

// 扩展 Document 接口以支持 IE 的 documentMode
declare global {
    interface Document {
        documentMode?: number;
    }
}

/**
 * 获取浏览器信息
 * @returns 浏览器信息对象
 */
export function getBrowserInfo(): BrowserInfo {
    const userAgent = window.navigator.userAgent;
    const browserInfo: BrowserInfo = {
        type: 'unknown',
        version: 'unknown',
        userAgent
    };

    if (document.documentMode) {
        browserInfo.type = 'IE';
        browserInfo.version = document.documentMode.toString();
    } else if (userAgent.includes('Firefox')) {
        browserInfo.type = 'Firefox';
        const match = userAgent.match(/Firefox\/([\d.]+)/);
        browserInfo.version = match ? match[1] : 'unknown';
    } else if (userAgent.includes('Opera')) {
        browserInfo.type = 'Opera';
        const match = userAgent.match(/Opera\/([\d.]+)/);
        browserInfo.version = match ? match[1] : 'unknown';
    } else if (userAgent.includes('Edg')) {
        browserInfo.type = 'Edg';
        const match = userAgent.match(/Edg\/([\d.]+)/);
        browserInfo.version = match ? match[1] : 'unknown';
    } else if (userAgent.includes('Chrome')) {
        browserInfo.type = 'Chrome';
        const match = userAgent.match(/Chrome\/([\d.]+)/);
        browserInfo.version = match ? match[1] : 'unknown';
    } else if (userAgent.includes('Safari')) {
        browserInfo.type = 'Safari';
        const match = userAgent.match(/Safari\/([\d.]+)/);
        browserInfo.version = match ? match[1] : 'unknown';
    }

    return browserInfo;
}

/**
 * 检查浏览器版本是否满足最低要求
 * @returns 是否满足最低版本要求
 */
export function checkBrowserVersion(): boolean {
    const minVer: MinVersionMap = {
        Chrome: 71,
        Firefox: 65,
        Safari: 12,
        Edg: 97,
        IE: 999
    };

    const browserInfo = getBrowserInfo();
    const majorVer = parseInt(browserInfo.version);

    return majorVer >= (minVer[browserInfo.type] || 999);
}
