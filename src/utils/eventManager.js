// 导入日志工具
import loggerFactory from '@/utils/logger';
// 创建自定义前缀的MQTT日志记录器
const logger = loggerFactory.createLogger('EventManager');

/**
 * 事件管理器 - 集中管理所有事件
 * 提供全局事件注册、触发和清理功能
 */

// 存储所有事件监听器 {eventName: [{handler, context, boundHandler}]}
const listeners = new Map();

// 存储所有定时器
const timers = new Set();

// 存储所有自定义事件
const customEvents = new Map();

/**
 * 添加事件监听器
 * @param {string} eventName 事件名称
 * @param {Function} handler 事件处理函数
 * @param {Object} context 上下文对象
 * @returns {Function} 绑定后的处理函数
 */
function addEventListener(eventName, handler, context) {
    if (!eventName || typeof handler !== 'function') {
        logger.error('添加事件监听器失败：参数无效', { eventName, handler });
        return null;
    }

    // 创建绑定的处理函数
    const boundHandler = context ? handler.bind(context) : handler;

    // 添加到监听器列表
    if (!listeners.has(eventName)) {
        listeners.set(eventName, []);
    }

    const eventListeners = listeners.get(eventName);
    const listenerInfo = { handler, context, boundHandler };
    eventListeners.push(listenerInfo);

    // 添加到DOM
    window.addEventListener(eventName, boundHandler);

    return listenerInfo;
}

/**
 * 移除事件监听器
 * @param {string} eventName 事件名称
 * @param {Function} handler 事件处理函数
 * @param {Object} context 上下文对象
 */
function removeEventListener(eventName, handler, context) {
    if (!eventName || !listeners.has(eventName)) {
        return;
    }

    const eventListeners = listeners.get(eventName);

    // 查找匹配的监听器
    const index = eventListeners.findIndex(listener =>
        listener.handler === handler && listener.context === context);

    if (index !== -1) {
        const { boundHandler } = eventListeners[index];

        // 从DOM移除
        window.removeEventListener(eventName, boundHandler);

        // 从列表中移除
        eventListeners.splice(index, 1);

        logger.info(`已移除事件监听器: ${eventName}`);

        // 如果该事件没有监听器了，从Map中移除
        if (eventListeners.length === 0) {
            listeners.delete(eventName);
        }
    }
}

/**
 * 移除所有事件监听器
 * @param {string} [eventName] 事件名称，如果不提供则移除所有事件的监听器
 */
function removeAllEventListeners(eventName) {
    if (eventName) {
        // 移除指定事件的所有监听器
        if (listeners.has(eventName)) {
            const eventListeners = listeners.get(eventName);

            // 遍历该事件的所有监听器
            for (const { boundHandler } of eventListeners) {
                // 从DOM移除
                window.removeEventListener(eventName, boundHandler);
            }

            // 清空监听器列表
            listeners.delete(eventName);
            logger.info(`已移除事件 ${eventName} 的所有监听器`);
        }
    } else {
        // 移除所有事件的所有监听器
        for (const [eventName, eventListeners] of listeners.entries()) {
            // 遍历该事件的所有监听器
            for (const { boundHandler } of eventListeners) {
                // 从DOM移除
                window.removeEventListener(eventName, boundHandler);
            }
        }

        // 清空监听器列表
        listeners.clear();
        logger.info('已移除所有事件监听器');
    }
}

/**
 * 触发自定义事件
 * @param {string} eventName 事件名称
 * @param {any} data 事件数据
 */
function emit(eventName, data) {
    const event = new CustomEvent(eventName, { detail: data });
    window.dispatchEvent(event);

    logger.group(`触发事件 ${eventName}`, true);
    logger.info(`已触发事件: ${eventName}`, data);
    logger.groupEnd();
}

/**
 * 注册自定义事件
 * @param {string} eventName 事件名称
 * @param {string} description 事件描述
 */
function registerEvent(eventName, description) {
    logger.group(`注册事件 ${eventName}`, true);
    if (customEvents.has(eventName)) {
        logger.info(`事件 ${eventName} 已存在，将被覆盖`);
    }

    customEvents.set(eventName, { name: eventName, description });
    logger.info(`已注册事件: ${eventName} - ${description}`);
    logger.groupEnd();
}

/**
 * 获取所有注册的自定义事件
 * @returns {Array} 事件列表
 */
function getRegisteredEvents() {
    return Array.from(customEvents.values());
}

/**
 * 设置定时器
 * @param {Function} callback 回调函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {number} 定时器ID
 */
function setInterval(callback, delay) {
    const timerId = window.setInterval(callback, delay);
    timers.add(timerId);
    return timerId;
}

/**
 * 清除定时器
 * @param {number} timerId 定时器ID
 */
function clearInterval(timerId) {
    if (timers.has(timerId)) {
        window.clearInterval(timerId);
        timers.delete(timerId);
    }
}

/**
 * 清除所有定时器
 */
function clearAllIntervals() {
    for (const timerId of timers) {
        window.clearInterval(timerId);
    }
    timers.clear();
    logger.info('已清除所有定时器');
}

// 导出事件管理器
export default {
    addEventListener,
    removeEventListener,
    removeAllEventListeners,
    emit,
    registerEvent,
    getRegisteredEvents,
    setInterval,
    clearInterval,
    clearAllIntervals
};
