import storageConfig from '@/config/storage';
import tool from '@/utils/tool';

/**
 * 用户信息接口
 */
interface UserInfo {
    role?: string[];
    [key: string]: any;
}

/**
 * 是否含有不限分类，有则表示全部允许通过
 * @returns 是否拥有全部权限
 */
export function permissionAll(): boolean {
    const allPermissions = '*/*/*';
    const permissions: string[] = tool.data.get(storageConfig.vars.userPermission) || [];
    return permissions.includes(allPermissions);
}

/**
 * 比对两组数据是否一致
 * @param news 新数据数组
 * @param old 旧数据数组
 * @returns 是否一致
 */
export function judementSameArr(news: any[], old: any[]): boolean {
    let count = 0;
    const leng = news.length;

    for (const newItem of news) {
        for (const oldItem of old) {
            if (newItem === oldItem) {
                count++;
                break; // 找到匹配项后跳出内层循环
            }
        }
    }

    return count === leng;
}

/**
 * 检查用户是否拥有指定权限
 * @param data 权限标识
 * @returns 是否拥有权限
 */
export function permission(data: string): boolean {
    const permissions: string[] = tool.data.get(storageConfig.vars.userPermission);
    if (!permissions) {
        return false;
    }
    return permissions.includes(data);
}

/**
 * 检查用户是否拥有指定角色权限
 * @param data 角色标识
 * @returns 是否拥有角色权限
 */
export function rolePermission(data: string): boolean {
    const userInfo: UserInfo = tool.data.get(storageConfig.vars.userInfo);
    if (!userInfo) {
        return false;
    }

    const role = userInfo.role;
    if (!role) {
        return false;
    }

    return role.includes(data);
}
