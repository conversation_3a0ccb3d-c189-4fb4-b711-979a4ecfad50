import storageConfig from '@/config/storage';
import tool from '@/utils/tool';

/**
 * 用户信息接口
 */
interface UserInfo {
    role?: string[];
    [key: string]: any;
}

/**
 * 是否含有不限分类，有则表示全部允许通过
 * @returns 是否拥有全部权限
 */
export function permissionAll(): boolean {
    /*const allPermissions = '*!/!*!/!*';
    let permissions = tool.data.get(storageConfig.vars.userPermission);
    return permissions.includes(allPermissions);*/
    return true;
}

/**
 * 比对两组数据是否一致
 * @param news 新数据数组
 * @param old 旧数据数组
 * @returns 是否一致
 */
export function judementSameArr(news: any[], old: any[]): boolean {
    // console.log(news)
    // console.log(old)
    let count = 0;
    const leng = news.length;
    for (const newItem of news) {
        for (const oldItem of old) {
            if (newItem === oldItem) {
                count++;
                // console.log(newItem)
                break; // 找到匹配项后跳出内层循环
            }
        }
    }
    // console.log('相同的数量', count)
    return count === leng;
}

/**
 * 检查用户是否拥有指定权限
 * @param data 权限标识
 * @returns 是否拥有权限
 */
export function permission(data: string): boolean {
    /*let permissions = tool.data.get(storageConfig.vars.userPermission);
    if (!permissions) {
        return false;
    }
    let isHave = permissions.includes(data);
    return isHave;*/
    return true;
}

/**
 * 检查用户是否拥有指定角色权限
 * @param data 角色标识
 * @returns 是否拥有角色权限
 */
export function rolePermission(data: string): boolean {
    /*let userInfo = tool.data.get(storageConfig.vars.userInfo);
    if (!userInfo) {
        return false;
    }
    let role = userInfo.role;
    if (!role) {
        return false;
    }
    let isHave = role.includes(data);
    return isHave;*/
    return true;
}
