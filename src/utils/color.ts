/**
 * 颜色工具类
 * 提供颜色格式转换和颜色调整功能
 */
class ColorUtils {
    /**
     * hex颜色转rgb颜色
     * @param str hex颜色字符串，如 "#ff0000" 或 "ff0000"
     * @returns RGB数组 [r, g, b]
     */
    HexToRgb(str: string): number[] {
        str = str.replace('#', '');
        const hxs = str.match(/../g);
        if (!hxs || hxs.length !== 3) {
            throw new Error('Invalid hex color format');
        }

        const result: number[] = [];
        for (let i = 0; i < 3; i++) {
            result[i] = parseInt(hxs[i], 16);
        }
        return result;
    }

    /**
     * rgb颜色转hex颜色
     * @param a 红色分量 (0-255)
     * @param b 绿色分量 (0-255)
     * @param c 蓝色分量 (0-255)
     * @returns hex颜色字符串
     */
    RgbToHex(a: number, b: number, c: number): string {
        const hexs = [a.toString(16), b.toString(16), c.toString(16)];
        for (let i = 0; i < 3; i++) {
            if (hexs[i].length === 1) {
                hexs[i] = '0' + hexs[i];
            }
        }
        return '#' + hexs.join('');
    }

    /**
     * 加深颜色
     * @param color hex颜色字符串
     * @param level 加深程度 (0-1)
     * @returns 加深后的hex颜色字符串
     */
    darken(color: string, level: number): string {
        const rgbc = this.HexToRgb(color);
        for (let i = 0; i < 3; i++) {
            rgbc[i] = Math.floor(rgbc[i] * (1 - level));
        }
        return this.RgbToHex(rgbc[0], rgbc[1], rgbc[2]);
    }

    /**
     * 变淡颜色
     * @param color hex颜色字符串
     * @param level 变淡程度 (0-1)
     * @returns 变淡后的hex颜色字符串
     */
    lighten(color: string, level: number): string {
        const rgbc = this.HexToRgb(color);
        for (let i = 0; i < 3; i++) {
            rgbc[i] = Math.floor((255 - rgbc[i]) * level + rgbc[i]);
        }
        return this.RgbToHex(rgbc[0], rgbc[1], rgbc[2]);
    }
}

// 创建单例实例
const colorUtils = new ColorUtils();

export default colorUtils;
