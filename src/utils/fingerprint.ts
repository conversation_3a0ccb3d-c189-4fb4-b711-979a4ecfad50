import FingerprintJS from '@fingerprintjs/fingerprintjs';

/**
 * 浏览器指纹识别工具类
 * 提供稳定的浏览器指纹获取功能，支持缓存和防重复请求
 */
class Fingerprint {
    private fpCache: string | null = null;
    private fpPromise: Promise<string> | null = null;

    /**
     * 获取稳定的浏览器指纹
     * @returns Promise<string> 浏览器指纹ID
     */
    async getStableFingerprint(): Promise<string> {
        // 如果已有缓存，直接返回
        if (this.fpCache) {
            return this.fpCache;
        }

        // 如果正在获取中，返回正在进行的Promise
        if (this.fpPromise) {
            return this.fpPromise;
        }

        // 创建新的获取指纹Promise
        this.fpPromise = new Promise(async (resolve, reject) => {
            try {
                const fp = await FingerprintJS.load();
                const result = await fp.get();
                this.fpCache = result.visitorId;
                this.fpPromise = null;
                resolve(this.fpCache);
            } catch (error) {
                this.fpPromise = null;
                reject(error);
            }
        });

        return this.fpPromise;
    }

    /**
     * 清除缓存（需要时可调用）
     */
    clearCache(): void {
        this.fpCache = null;
        this.fpPromise = null;
    }
}

// 导出单例实例
export default new Fingerprint();
