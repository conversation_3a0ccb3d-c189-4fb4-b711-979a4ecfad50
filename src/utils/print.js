/**
 * 打印工具类
 * 提供页面元素打印功能
 */

// 打印选项接口
interface PrintOptions {
    noPrint?: string;
    [key: string]: any;
}

// Vue组件接口
interface VueComponent {
    $el: HTMLElement;
}

// DOM或Vue组件类型
type DomOrVue = string | HTMLElement | VueComponent;

/**
 * 打印类
 */
class Print {
    private options: PrintOptions;
    private dom: HTMLElement | null = null;

    constructor(dom: DomOrVue, options?: PrintOptions) {
        this.options = this.extend(
            {
                noPrint: '.no-print'
            },
            options || {}
        );

        if (typeof dom === 'string') {
            try {
                this.dom = document.querySelector(dom);
            } catch {
                const createDom = document.createElement('div');
                createDom.innerHTML = dom;
                this.dom = createDom;
            }
        } else {
            this.dom = this.isDOM(dom) ? (dom as HTMLElement) : (dom as VueComponent).$el;
        }

        this.init();
    }
    /**
     * 初始化打印
     */
    private init(): void {
        if (!this.dom) {
            console.error('Print: DOM element not found');
            return;
        }
        const content = this.getStyle() + this.getHtml();
        this.writeIframe(content);
    }

    /**
     * 扩展对象属性
     */
    private extend(obj: any, obj2: any): any {
        for (const k in obj2) {
            obj[k] = obj2[k];
        }
        return obj;
    }

    /**
     * 获取样式
     */
    private getStyle(): string {
        let str = '';
        const styles = document.querySelectorAll('style,link');
        for (let i = 0; i < styles.length; i++) {
            str += styles[i].outerHTML;
        }
        str += '<style>' + (this.options.noPrint ? this.options.noPrint : '.no-print') + '{display:none;}</style>';
        str += '<style>html,body{background-color:#fff;}</style>';
        return str;
    }

    /**
     * 获取HTML内容
     */
    private getHtml(): string {
        if (!this.dom) return '';

        const inputs = document.querySelectorAll('input');
        const textareas = document.querySelectorAll('textarea');
        const selects = document.querySelectorAll('select');

        // 处理input元素
        for (let k = 0; k < inputs.length; k++) {
            const input = inputs[k] as HTMLInputElement;
            if (input.type === 'checkbox' || input.type === 'radio') {
                if (input.checked) {
                    input.setAttribute('checked', 'checked');
                } else {
                    input.removeAttribute('checked');
                }
            } else {
                input.setAttribute('value', input.value);
            }
        }

        // 处理textarea元素
        for (let k2 = 0; k2 < textareas.length; k2++) {
            const textarea = textareas[k2] as HTMLTextAreaElement;
            textarea.innerHTML = textarea.value;
        }

        // 处理select元素
        for (let k3 = 0; k3 < selects.length; k3++) {
            const select = selects[k3] as HTMLSelectElement;
            if (select.type === 'select-one') {
                const children = select.children;
                for (let i = 0; i < children.length; i++) {
                    const option = children[i] as HTMLOptionElement;
                    if (option.tagName === 'OPTION') {
                        if (option.selected) {
                            option.setAttribute('selected', 'selected');
                        } else {
                            option.removeAttribute('selected');
                        }
                    }
                }
            }
        }

        return this.dom.outerHTML;
    }

    /**
     * 写入iframe并执行打印
     */
    private writeIframe(content: string): void {
        const iframe = document.createElement('iframe');
        const f = document.body.appendChild(iframe);
        iframe.id = 'myIframe';
        iframe.setAttribute('style', 'position:absolute;width:0;height:0;top:-10px;left:-10px;');

        const w = f.contentWindow || f.contentDocument;
        const doc = f.contentDocument || (f.contentWindow && f.contentWindow.document);

        if (!doc) {
            console.error('Print: Unable to access iframe document');
            return;
        }

        doc.open();
        doc.write(content);
        doc.close();

        iframe.onload = () => {
            if (w) {
                this.toPrint(w as Window);
            }
            setTimeout(() => {
                if (document.body.contains(iframe)) {
                    document.body.removeChild(iframe);
                }
            }, 100);
        };
    }

    /**
     * 执行打印
     */
    private toPrint(frameWindow: Window): void {
        try {
            setTimeout(() => {
                frameWindow.focus();
                try {
                    if (!frameWindow.document.execCommand('print', false, null)) {
                        frameWindow.print();
                    }
                } catch (e) {
                    frameWindow.print();
                }
                frameWindow.close();
            }, 10);
        } catch (err) {
            console.log('Print error:', err);
        }
    }

    /**
     * 检查是否为DOM元素
     */
    private isDOM(obj: any): obj is HTMLElement {
        return typeof HTMLElement === 'object'
            ? obj instanceof HTMLElement
            : obj && typeof obj === 'object' && obj.nodeType === 1 && typeof obj.nodeName === 'string';
    }
}

/**
 * 打印工厂函数
 * @param dom DOM元素、选择器或Vue组件
 * @param options 打印选项
 * @returns Print实例
 */
function createPrint(dom: DomOrVue, options?: PrintOptions): Print {
    return new Print(dom, options);
}

export default createPrint;
