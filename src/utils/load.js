/**
 * 动态资源加载工具
 * 提供异步加载远程JS和CSS的功能
 */

// 扩展 Window 接口以支持动态属性
declare global {
    interface Window {
        [key: string]: any;
    }
}

/**
 * 异步加载远程JS
 * @param src 需要加载的URL路径
 * @param keyName 唯一key和JS返回的全局的对象名
 * @param callbackName 如果远程JS有callback，则可更有效的判断是否完成加载
 * @returns Promise，resolve时返回加载的全局对象
 */
export function loadJS(src: string, keyName: string, callbackName?: string): Promise<any> {
    return new Promise((resolve, reject) => {
        const has = document.head.querySelector(`script[loadKey="${keyName}"]`);
        if (has) {
            return resolve(window[keyName]);
        }

        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = src;
        script.setAttribute('loadKey', keyName);
        document.head.appendChild(script);

        script.onload = () => {
            if (callbackName) {
                window[callbackName] = () => {
                    return resolve(window[keyName]);
                };
            } else {
                setTimeout(() => {
                    return resolve(window[keyName]);
                }, 50);
            }
        };

        script.onerror = (err: Event | string) => {
            return reject(err);
        };
    });
}

/**
 * 异步加载远程CSS
 * @param src 需要加载的URL路径
 * @param keyName 唯一key
 * @returns Promise，加载完成时resolve
 */
export function loadCSS(src: string, keyName: string): Promise<void> {
    return new Promise((resolve, reject) => {
        const has = document.head.querySelector(`link[loadKey="${keyName}"]`);
        if (has) {
            return resolve();
        }

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = src;
        link.setAttribute('loadKey', keyName);
        document.head.appendChild(link);

        link.onload = () => {
            return resolve();
        };

        link.onerror = (err: Event | string) => {
            return reject(err);
        };
    });
}
