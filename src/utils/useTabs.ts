import { nextTick } from 'vue';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import router from '@/router';
import { useKeepAliveStore } from '@/stores/keepAlive';
import { useIframeStore } from '@/stores/iframe';
import { useViewTabsStore } from '@/stores/viewTabs';
import type { RouteLocationNormalizedLoaded } from 'vue-router';

// 路由标签接口
interface TabRoute {
    name?: string | symbol;
    fullPath: string;
    meta?: {
        affix?: boolean;
        [key: string]: any;
    };
    [key: string]: any;
}

// 回调函数类型
type NextCallback = (tabList: TabRoute[]) => void;

/**
 * 标签页管理工具
 */
class TabsManager {
    private keepAliveStore = useKeepAliveStore();
    private iframeStore = useIframeStore();
    private viewTabsStore = useViewTabsStore();

    /**
     * 刷新当前标签
     */
    refresh(): void {
        NProgress.start();
        const route = router.currentRoute.value;
        this.keepAliveStore.removeKeepLive(route.name);
        this.keepAliveStore.setRouteShow(false);
        nextTick(() => {
            this.keepAliveStore.pushKeepLive(route.name);
            this.keepAliveStore.setRouteShow(true);
            NProgress.done();
        });
    }

    /**
     * 关闭标签
     * @param tab 要关闭的标签，不传则关闭当前标签
     */
    close(tab?: TabRoute): void {
        const route = tab || router.currentRoute.value;

        this.viewTabsStore.removeViewTabs(route);
        this.iframeStore.removeIframeList(route);
        this.keepAliveStore.removeKeepLive(route.name);

        const tabList = this.viewTabsStore.viewTabs;
        const latestView = tabList.slice(-1)[0];

        if (latestView) {
            router.push(latestView);
        } else {
            router.push('/');
        }
    }

    /**
     * 关闭标签后处理
     * @param next 回调函数
     */
    closeNext(next?: NextCallback): void {
        const route = router.currentRoute.value;

        this.viewTabsStore.removeViewTabs(route);
        this.iframeStore.removeIframeList(route);
        this.keepAliveStore.removeKeepLive(route.name);

        if (next) {
            const tabList = this.viewTabsStore.viewTabs;
            next(tabList);
        }
    }

    /**
     * 关闭其他标签
     */
    closeOther(): void {
        const route = router.currentRoute.value;
        const tabList = [...this.viewTabsStore.viewTabs];

        tabList.forEach(tab => {
            if ((tab.meta && tab.meta.affix) || route.fullPath === tab.fullPath) {
                return;
            } else {
                this.close(tab);
            }
        });
    }

    /**
     * 设置标签标题
     * @param title 新标题
     */
    setTitle(title: string): void {
        this.viewTabsStore.updateViewTabsTitle(title);
    }
}

// 创建单例实例
const tabsManager = new TabsManager();

export default tabsManager;
