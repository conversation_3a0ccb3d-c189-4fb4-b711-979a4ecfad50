/**
 * 设备状态工具类
 * 提供设备状态相关的常用工具方法
 */
import deviceOnlineService from '@/services/deviceOnlineService';
import { formatTimestamp } from '@/utils/date';

/**
 * 设备状态工具类
 */
export const deviceStatusUtils = {
    /**
     * 获取设备状态显示文本
     * @param {boolean} online 是否在线
     * @param {boolean} mqttConnected MQTT是否连接
     * @returns {string} 状态文本
     */
    getStatusText(online, mqttConnected = true) {
        if (!mqttConnected) {
            return online ? '可能在线' : '可能离线';
        }
        return online ? '在线' : '离线';
    },

    /**
     * 获取设备状态类型（用于UI样式）
     * @param {boolean} online 是否在线
     * @param {boolean} mqttConnected MQTT是否连接
     * @returns {string} 状态类型
     */
    getStatusType(online, mqttConnected = true) {
        if (!mqttConnected) {
            return 'warning'; // 断网时使用警告色
        }
        return online ? 'success' : 'info';
    },

    /**
     * 格式化心跳时间提示
     * @param {number} lastHeartbeat 最后心跳时间戳
     * @param {boolean} mqttConnected MQTT是否连接
     * @returns {string} 提示文本
     */
    getHeartbeatTooltip(lastHeartbeat, mqttConnected = true) {
        if (!mqttConnected) {
            return '网络已断开，设备状态可能不准确';
        }

        if (!lastHeartbeat || lastHeartbeat === 0) {
            return '设备未发送过心跳';
        }

        const lastHeartbeatTime = formatTimestamp(lastHeartbeat);

        // 如果时间格式化失败，直接返回错误信息
        if (lastHeartbeatTime === '时间格式错误' || lastHeartbeatTime === '无效时间') {
            return `心跳时间异常: ${lastHeartbeat}`;
        }

        // 计算时间差，需要确保使用正确的时间戳格式
        let heartbeatMs = lastHeartbeat;
        if (lastHeartbeat.toString().length === 10) {
            heartbeatMs = lastHeartbeat * 1000;
        }

        const timeSinceLastHeartbeat = Math.floor((Date.now() - heartbeatMs) / 1000);

        // 如果时间差为负数或过大，可能是时间戳有问题
        if (timeSinceLastHeartbeat < 0) {
            return `心跳时间异常: ${lastHeartbeatTime}`;
        }

        let timeText = '';
        if (timeSinceLastHeartbeat < 60) {
            timeText = `${timeSinceLastHeartbeat}秒前`;
        } else if (timeSinceLastHeartbeat < 3600) {
            timeText = `${Math.floor(timeSinceLastHeartbeat / 60)}分钟前`;
        } else if (timeSinceLastHeartbeat < 86400) {
            timeText = `${Math.floor(timeSinceLastHeartbeat / 3600)}小时前`;
        } else {
            timeText = `${Math.floor(timeSinceLastHeartbeat / 86400)}天前`;
        }

        return `最后心跳: ${lastHeartbeatTime} (${timeText})`;
    },

    /**
     * 检查设备是否可以操作（在线且MQTT连接正常）
     * @param {string|number} deviceId 设备ID
     * @returns {boolean} 是否可以操作
     */
    canOperateDevice(deviceId) {
        const isOnline = deviceOnlineService.isDeviceOnline(deviceId);
        const mqttConnected = deviceOnlineService.mqttConnected;
        return isOnline && mqttConnected;
    },

    /**
     * 批量检查设备状态
     * @param {Array<string|number>} deviceIds 设备ID数组
     * @returns {Object} 设备状态映射 {deviceId: {online, canOperate}}
     */
    batchCheckDeviceStatus(deviceIds) {
        const result = {};
        const mqttConnected = deviceOnlineService.mqttConnected;

        deviceIds.forEach(deviceId => {
            const isOnline = deviceOnlineService.isDeviceOnline(deviceId);
            result[deviceId] = {
                online: isOnline,
                canOperate: isOnline && mqttConnected,
                mqttConnected
            };
        });

        return result;
    },

    /**
     * 获取设备状态统计
     * @param {Array<string|number>} deviceIds 设备ID数组
     * @returns {Object} 统计信息
     */
    getDeviceStatusStats(deviceIds) {
        const statusMap = this.batchCheckDeviceStatus(deviceIds);
        const stats = {
            total: deviceIds.length,
            online: 0,
            offline: 0,
            canOperate: 0
        };

        Object.values(statusMap).forEach(status => {
            if (status.online) {
                stats.online++;
            } else {
                stats.offline++;
            }
            
            if (status.canOperate) {
                stats.canOperate++;
            }
        });

        return stats;
    },

    /**
     * 格式化设备状态为显示文本
     * @param {Object} deviceStatus 设备状态对象
     * @returns {string} 格式化的状态文本
     */
    formatDeviceStatus(deviceStatus) {
        if (!deviceStatus) {
            return '未知状态';
        }

        const { online, lastHeartbeat, lastUpdate } = deviceStatus;
        const statusText = online ? '在线' : '离线';

        if (lastHeartbeat && lastHeartbeat !== 0) {
            const heartbeatTime = formatTimestamp(lastHeartbeat);
            // 检查时间格式化是否成功
            if (heartbeatTime && heartbeatTime !== '时间格式错误' && heartbeatTime !== '无效时间') {
                return `${statusText} (最后心跳: ${heartbeatTime})`;
            }
        }

        if (lastUpdate && lastUpdate !== 0) {
            const updateTime = formatTimestamp(lastUpdate);
            // 检查时间格式化是否成功
            if (updateTime && updateTime !== '时间格式错误' && updateTime !== '无效时间') {
                return `${statusText} (最后更新: ${updateTime})`;
            }
        }

        return statusText;
    }
};

export default deviceStatusUtils;
