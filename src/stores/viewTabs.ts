import { defineStore } from 'pinia';
import router from '@/router';
import type { RouteLocationNormalized } from 'vue-router';

// 视图标签项类型
export interface ViewTab {
    fullPath: string;
    name?: string | symbol;
    path: string;
    meta: {
        title?: string;
        scrollTop?: number;
        [key: string]: any;
    };
    [key: string]: any;
}

// 视图标签状态类型定义
export interface ViewTabsState {
    viewTabs: ViewTab[];
}

export const useViewTabsStore = defineStore('viewTabsStore', {
    state: (): ViewTabsState => {
        return {
            viewTabs: []
        };
    },
    getters: {},
    actions: {
        pushViewTabs(route: ViewTab): void {
            const backPathIndex = this.viewTabs.findIndex(item => item.fullPath === router.options.history.state.back);
            const target = this.viewTabs.find(item => item.fullPath === route.fullPath);
            const isName = route.name;
            if (!target && isName) {
                if (backPathIndex === -1) {
                    this.viewTabs.push(route);
                } else {
                    this.viewTabs.splice(backPathIndex + 1, 0, route);
                }
            }
        },
        removeViewTabs(route: ViewTab): void {
            this.viewTabs.forEach((item, index) => {
                if (item.fullPath === route.fullPath) {
                    this.viewTabs.splice(index, 1);
                }
            });
        },
        updateViewTabs(route: Partial<ViewTab>): void {
            this.viewTabs.forEach(item => {
                if (item.fullPath === route.fullPath) {
                    Object.assign(item, route);
                }
            });
        },
        updateViewTabsTitle(title: string = ''): void {
            const nowFullPath = location.hash.substring(1);
            this.viewTabs.forEach(item => {
                if (item.fullPath === nowFullPath) {
                    item.meta.title = title;
                }
            });
        },
        clearViewTabs(): void {
            this.viewTabs = [];
        }
    }
});
