import { defineStore } from 'pinia';
import notification from '@/api/model/notification';
import { useGlobalStore } from '@/stores/global';

// 通知状态类型定义
export interface NotificationState {
    unreadCount: number;
    lastUpdateTime: number;
}

// 未读消息响应类型
export interface UnreadCountResponse {
    status: number;
    data?: {
        count: number;
    };
}

let unreadCountTimer: NodeJS.Timeout | null = null;

export const useNotificationStore = defineStore('notification', {
    state: (): NotificationState => ({
        unreadCount: 0,
        lastUpdateTime: 0
    }),

    actions: {
        async getUnreadCount(): Promise<void> {
            const globalStore = useGlobalStore();

            // 用户已退出时不再发起请求
            if (globalStore.userTokenExpired) {
                return;
            }

            // 防止300ms内重复请求
            const now = Date.now();
            if (now - this.lastUpdateTime < 300) {
                return;
            }

            if (unreadCountTimer) {
                clearTimeout(unreadCountTimer);
            }

            unreadCountTimer = setTimeout(async () => {
                try {
                    const response = await notification.unreadCount.get() as unknown as UnreadCountResponse;
                    if (response.status === 1 && response.data?.count !== undefined && response.data.count >= 0) {
                        this.unreadCount = response.data.count;
                        this.lastUpdateTime = Date.now();
                    }
                } catch (error) {
                    console.error('获取未读消息数量失败:', error);
                }
                unreadCountTimer = null;
            }, 300);
        },

        /**
         * 设置未读消息数量
         * @param count 未读消息数量
         */
        setUnreadCount(count: number): void {
            this.unreadCount = Math.max(0, count);
            this.lastUpdateTime = Date.now();
        },

        /**
         * 增加未读消息数量
         * @param increment 增加的数量，默认为1
         */
        incrementUnreadCount(increment: number = 1): void {
            this.unreadCount += increment;
            this.lastUpdateTime = Date.now();
        },

        /**
         * 减少未读消息数量
         * @param decrement 减少的数量，默认为1
         */
        decrementUnreadCount(decrement: number = 1): void {
            this.unreadCount = Math.max(0, this.unreadCount - decrement);
            this.lastUpdateTime = Date.now();
        },

        /**
         * 清空未读消息数量
         */
        clearUnreadCount(): void {
            this.unreadCount = 0;
            this.lastUpdateTime = Date.now();
        }
    }
});
