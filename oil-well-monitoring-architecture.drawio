<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.1">
  <diagram name="油井注水设备监控系统架构图" id="system-architecture">
    <mxGraphModel dx="1901" dy="1261" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" background="none" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;startArrow=diamondThin;startFill=1;strokeWidth=3;strokeColor=#3A5431;exitX=0.75;exitY=0;exitDx=0;exitDy=0;fillColor=#6d8764;" edge="1" parent="1" source="emqx-broker" target="frontend-layer">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1010" y="948" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;strokeColor=#7B1FA2;startArrow=diamondThin;startFill=1;" edge="1" parent="1" source="emqx-broker" target="desktop-client-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="network-status" value="&lt;blockquote style=&quot;margin: 0 0 0 40px; border: none; padding: 0px;&quot;&gt;&lt;div style=&quot;line-height: 150%;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 有网：正常MQTT、HTTP通信&lt;/span&gt;&lt;/div&gt;&lt;/blockquote&gt;&lt;blockquote style=&quot;margin: 0 0 0 40px; border: none; padding: 0px;&quot;&gt;&lt;div style=&quot;line-height: 150%;&quot;&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 弱网：延迟发送&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 150%;&quot;&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 断网：本地数据缓存&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;，恢复后同步&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/blockquote&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8B878;strokeColor=#AC7739;align=left;fontColor=#443826;glass=0;shadow=0;gradientColor=none;imageAspect=1;gradientDirection=south;strokeWidth=2;fillStyle=auto;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="483" y="191" width="340" height="80" as="geometry" />
        </mxCell>
        <mxCell id="device-to-client-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#C1AD62,#CDBD7D);strokeWidth=3;" parent="1" source="oil-well-1" target="desktop-client-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="142" y="480" />
              <mxPoint x="176" y="480" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="device-to-client-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#C1AD62,#CDBD7D);strokeWidth=3;" parent="1" source="oil-well-2" target="desktop-client-2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="262" y="480" />
              <mxPoint x="326" y="480" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="device-to-client-n" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=light-dark(#C1AD62,#CDBD7D);strokeWidth=3;" parent="1" source="oil-well-n" target="desktop-client-2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="382" y="480" />
              <mxPoint x="326" y="480" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="emqx-to-mqtt-processor" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4caf50;strokeWidth=3;startArrow=diamondThin;startFill=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="comm-layer" target="Wam68u_NnZaqKmoTfJ2d-25" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1168" y="644" as="sourcePoint" />
            <mxPoint x="1609.9999999999977" y="897" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-11" value="串口通讯" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#AF9D5A;strokeColor=#4E4528;size=215.30769230769215;fontColor=#FFFFFF;gradientColor=#4E4528;fontStyle=1;rounded=1;" vertex="1" parent="1">
          <mxGeometry x="161" y="375" width="178" height="43" as="geometry" />
        </mxCell>
        <mxCell id="frontend-layer" value="云端前端应用层 (Cloud Frontend Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#6d8764;strokeColor=#3A5431;fontStyle=1;fontSize=14;strokeWidth=3;fontColor=#ffffff;" parent="1" vertex="1">
          <mxGeometry x="1261" y="62" width="400" height="255" as="geometry" />
        </mxCell>
        <mxCell id="vue3-app" value="Vue 3 + Vite&#xa;Web管理平台" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6d8764;strokeColor=#3A5431;strokeWidth=3;fontColor=#ffffff;" parent="frontend-layer" vertex="1">
          <mxGeometry x="20" y="42" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pinia-store" value="Pinia&#xa;状态管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6d8764;strokeColor=#3A5431;strokeWidth=3;fontColor=#ffffff;" parent="frontend-layer" vertex="1">
          <mxGeometry x="180" y="38" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="echarts" value="ECharts&#xa;图表组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6d8764;strokeColor=#3A5431;strokeWidth=3;fontColor=#ffffff;" parent="frontend-layer" vertex="1">
          <mxGeometry x="302" y="38" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="sass-styles" value="Sass/SCSS&#xa;样式系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6d8764;strokeColor=#3A5431;strokeWidth=3;fontColor=#ffffff;" parent="frontend-layer" vertex="1">
          <mxGeometry x="180" y="88" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="element-plus" value="Element Plus&#xa;UI组件库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6d8764;strokeColor=#3A5431;strokeWidth=3;fontColor=#ffffff;" parent="frontend-layer" vertex="1">
          <mxGeometry x="302" y="88" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="frontend-functions" value="&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 设备远程监控 • 历史数据分析&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 用户权限管理 • 主题规则配置&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 通过上位机间接控制设备&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;• 发布消息通知（全局、针对商户、针对商户设备等）&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;br&gt;&lt;/span&gt;•&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;•&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;•&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6d8764;strokeColor=#3A5431;strokeWidth=3;fontColor=#ffffff;" parent="frontend-layer" vertex="1">
          <mxGeometry x="17" y="140" width="366" height="98" as="geometry" />
        </mxCell>
        <mxCell id="devops-layer" value="监控运维层 (DevOps Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#76608a;strokeColor=#432D57;fontStyle=1;fontSize=14;flipH=1;fontColor=#ffffff;strokeWidth=3;" parent="1" vertex="1">
          <mxGeometry x="197" y="1061" width="419" height="133" as="geometry" />
        </mxCell>
        <mxCell id="zabbix" value="Zabbix&#xa;系统监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#76608a;strokeColor=#432D57;fontColor=#ffffff;strokeWidth=3;" parent="devops-layer" vertex="1">
          <mxGeometry x="120" y="52" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="grafana" value="Grafana&#xa;监控面板" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#76608a;strokeColor=#432D57;fontColor=#ffffff;strokeWidth=3;" parent="devops-layer" vertex="1">
          <mxGeometry x="20" y="52" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="elk-stack" value="ELK Stack&#xa;日志管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#76608a;strokeColor=#432D57;fontColor=#ffffff;strokeWidth=3;" parent="devops-layer" vertex="1">
          <mxGeometry x="320" y="52" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="docker" value="Docker&#xa;容器化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#76608a;strokeColor=#432D57;fontColor=#ffffff;strokeWidth=3;" parent="devops-layer" vertex="1">
          <mxGeometry x="220" y="52" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="external-layer" value="外部服务层 (External Services)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f0a30a;strokeColor=#BD7000;fontStyle=1;fontSize=14;strokeWidth=3;fontColor=#FFFFFF;" parent="1" vertex="1">
          <mxGeometry x="197" y="1232" width="421" height="139" as="geometry" />
        </mxCell>
        <mxCell id="sms-service" value="短信服务&#xa;告警通知" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0a30a;strokeColor=#BD7000;strokeWidth=3;fontColor=#FFFFFF;" parent="external-layer" vertex="1">
          <mxGeometry x="20" y="50" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="email-service" value="邮件服务&#xa;SMTP推送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0a30a;strokeColor=#BD7000;strokeWidth=3;fontColor=#FFFFFF;" parent="external-layer" vertex="1">
          <mxGeometry x="120" y="50" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="map-service" value="地图服务&#xa;设备定位" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0a30a;strokeColor=#BD7000;strokeWidth=3;fontColor=#FFFFFF;" parent="external-layer" vertex="1">
          <mxGeometry x="220" y="50" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cloud-service" value="云服务&#xa;阿里云/腾讯云" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0a30a;strokeColor=#BD7000;strokeWidth=3;fontColor=#FFFFFF;" parent="external-layer" vertex="1">
          <mxGeometry x="320" y="50" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="data-layer" value="云端数据存储层 (Cloud Data Storage)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#C38A56;strokeColor=#4B331D;fontStyle=1;fontSize=14;fontColor=#FFFFFF;fillStyle=auto;strokeWidth=3;" parent="1" vertex="1">
          <mxGeometry x="846" y="1267" width="644" height="250" as="geometry" />
        </mxCell>
        <mxCell id="mysql-cluster" value="MySQL 集群&lt;br&gt;用户/设备/配置数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DD9C62;strokeColor=#4B331D;fontColor=#FFFFFF;fillStyle=auto;strokeWidth=2;" parent="data-layer" vertex="1">
          <mxGeometry x="30" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="redis-cluster" value="Redis 集群&#xa;实时数据缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DD9C62;strokeColor=#4B331D;fontColor=#FFFFFF;fillStyle=auto;strokeWidth=2;" parent="data-layer" vertex="1">
          <mxGeometry x="185" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="elasticsearch" value="Elasticsearch&#xa;历史数据/日志" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DD9C62;strokeColor=#4B331D;fontColor=#FFFFFF;fillStyle=auto;strokeWidth=2;" parent="data-layer" vertex="1">
          <mxGeometry x="339" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="file-storage" value="文件存储&#xa;报告/图片" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DD9C62;strokeColor=#4B331D;fontColor=#FFFFFF;fillStyle=auto;strokeWidth=2;" parent="data-layer" vertex="1">
          <mxGeometry x="494" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="data-flow" value="&lt;div style=&quot;line-height: 180%;&quot;&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));&quot;&gt;• 用户账号信息&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;• 设备注册信息&amp;nbsp;&lt;/div&gt;&lt;div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));&quot;&gt;• 注水历史数据&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));&quot;&gt;• 温度历史数据&amp;nbsp;&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;• 设备状态日志&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;• 告警日志追踪&lt;/div&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C38A56;strokeColor=#4B331D;fontColor=#FFFFFF;fillStyle=auto;strokeWidth=2;fontSize=14;gradientColor=none;horizontal=1;spacing=2;spacingTop=0;textDirection=vertical-lr;" parent="data-layer" vertex="1">
          <mxGeometry x="29" y="120" width="587" height="111" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-62" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;startArrow=diamond;startFill=0;endArrow=diamond;endFill=0;fillColor=#a0522d;strokeColor=#6D1F00;" edge="1" parent="1" source="backend-layer" target="data-layer">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="backend-layer" value="云端后端服务层 (Cloud Backend Services)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#616BBD;strokeColor=#372D66;fontStyle=1;fontSize=14;fontColor=#FFFFFF;strokeWidth=3;" parent="1" vertex="1">
          <mxGeometry x="848" y="916" width="640" height="251" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;strokeWidth=3;strokeColor=#8853AB;dashed=1;dashPattern=8 8;startArrow=block;startFill=1;endArrow=diamondThin;endFill=0;" edge="1" parent="backend-layer" source="php-swoole-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="315" y="-535" as="targetPoint" />
            <Array as="points">
              <mxPoint x="18" y="79" />
              <mxPoint x="18" y="-535" />
              <mxPoint x="314" y="-535" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="php-swoole-1" value="PHP + Swoole&lt;br&gt;用户认证API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#777BE6;strokeColor=#372D66;fontColor=#FFFFFF;strokeWidth=2;" parent="backend-layer" vertex="1">
          <mxGeometry x="30" y="49" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-2" value="PHP + Swoole&lt;br&gt;MQTT数据处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#777BE6;strokeColor=#372D66;fontColor=#FFFFFF;strokeWidth=2;" parent="backend-layer" vertex="1">
          <mxGeometry x="150" y="49" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-3" value="PHP + Swoole&lt;br&gt;WebSocket服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#777BE6;strokeColor=#372D66;fontColor=#FFFFFF;strokeWidth=2;" parent="backend-layer" vertex="1">
          <mxGeometry x="270" y="49" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-4" value="PHP + Swoole&lt;br&gt;设备控制API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#777BE6;strokeColor=#372D66;fontColor=#FFFFFF;strokeWidth=2;" parent="backend-layer" vertex="1">
          <mxGeometry x="390" y="49" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-5" value="PHP + Swoole&lt;br&gt;历史数据API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#777BE6;strokeColor=#372D66;fontColor=#FFFFFF;strokeWidth=2;" parent="backend-layer" vertex="1">
          <mxGeometry x="510" y="49" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swoole-processes" value="&lt;div style=&quot;line-height: 180%;&quot;&gt;&lt;div&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;• 商户管理&lt;div&gt;•&amp;nbsp;权限管理&lt;/div&gt;&lt;div&gt;• 登录验证&lt;/div&gt;&lt;div&gt;• 消息处理&lt;/div&gt;&lt;div&gt;• 数据操作&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));&quot;&gt;• 实时推送&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;&quot;&gt;• 日常运维&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#616BBD;strokeColor=#372D66;fontColor=#FFFFFF;strokeWidth=2;horizontal=1;align=center;textDirection=vertical-lr;flipH=0;flipV=1;verticalAlign=middle;" parent="backend-layer" vertex="1">
          <mxGeometry x="30" y="131" width="580" height="95" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-39" value="&lt;span style=&quot;color: rgb(255, 255, 255); text-align: left;&quot;&gt;Swoole进程管理：Master/Worker/Task&lt;/span&gt;" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" vertex="1" parent="backend-layer">
          <mxGeometry x="90" y="163.5" width="158" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=diamondThin;startFill=0;strokeWidth=3;dashed=1;dashPattern=8 8;strokeColor=#8853AB;" edge="1" parent="backend-layer" target="php-swoole-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="265" y="-477" as="sourcePoint" />
            <mxPoint x="59" y="49" as="targetPoint" />
            <Array as="points">
              <mxPoint x="81" y="-477" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="client-layer" value="上位机客户端层 (Desktop Client Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#6D4FAA;strokeColor=#4D3777;fontStyle=1;fontSize=14;fontColor=#ffffff;strokeWidth=3;swimlaneFillColor=none;gradientColor=none;rounded=0;labelBackgroundColor=none;swimlaneLine=1;glass=0;" parent="1" vertex="1">
          <mxGeometry x="50" y="50" width="400" height="260" as="geometry" />
        </mxCell>
        <mxCell id="desktop-client-1" value="上位机客户端 1&#xa;桌面软件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#956BE7;strokeColor=#624598;fontColor=#ffffff;strokeWidth=2;arcSize=50;" parent="client-layer" vertex="1">
          <mxGeometry x="67" y="40" width="120" height="53" as="geometry" />
        </mxCell>
        <mxCell id="client-functions" value="&lt;div style=&quot;line-height: 150%;&quot;&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 云端账号登录验证（HTTP）&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 设备控制（注水开关、流量调节）&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 数据采集（流量、温度等）&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• MQTT心跳发送（每10秒）&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 订阅消息通知、设备控制主题，以及发布数据主题等&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7250B1;strokeColor=#624598;fontColor=#FFFFFF;glass=0;shadow=0;strokeWidth=2;arcSize=11;gradientColor=none;" parent="client-layer" vertex="1">
          <mxGeometry x="30" y="150" width="340" height="100" as="geometry" />
        </mxCell>
        <mxCell id="desktop-client-2" value="上位机客户端 N&lt;br&gt;桌面软件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#956BE7;strokeColor=#624598;fontColor=#ffffff;strokeWidth=2;arcSize=50;" parent="client-layer" vertex="1">
          <mxGeometry x="218" y="73" width="120" height="53" as="geometry" />
        </mxCell>
        <mxCell id="device-layer" value="设备层 (Device Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#877945;strokeColor=#605531;fontStyle=1;fontSize=14;fontColor=#ffffff;textShadow=0;labelBorderColor=none;labelBackgroundColor=none;strokeWidth=3;rounded=0;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="60" y="540" width="400" height="250" as="geometry" />
        </mxCell>
        <mxCell id="oil-well-1" value="油井注水设备 1&lt;br&gt;注水泵/流量计/温度传感器/..." style="rounded=1;whiteSpace=wrap;html=1;fillColor=#AF9D5A;strokeColor=#605531;textShadow=0;labelBorderColor=none;labelBackgroundColor=none;arcSize=14;strokeWidth=2;perimeterSpacing=0;gradientColor=none;glass=0;fontColor=#FFFFFF;fontSize=12;horizontal=1;" parent="device-layer" vertex="1">
          <mxGeometry x="30" y="40" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="oil-well-2" value="油井注水设备 2&lt;br&gt;注水泵/流量计/温度传感器/..." style="rounded=1;whiteSpace=wrap;html=1;fillColor=#AF9D5A;strokeColor=#605531;textShadow=0;labelBorderColor=none;labelBackgroundColor=none;arcSize=12;strokeWidth=2;perimeterSpacing=0;gradientColor=none;glass=0;fontColor=#FFFFFF;fontSize=12;horizontal=1;" parent="device-layer" vertex="1">
          <mxGeometry x="150" y="40" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="oil-well-n" value="油井注水设备 N&lt;br&gt;注水泵/流量计/温度传感器/..." style="rounded=1;whiteSpace=wrap;html=1;fillColor=#AF9D5A;strokeColor=#605531;textShadow=0;labelBorderColor=none;labelBackgroundColor=none;arcSize=12;strokeWidth=2;perimeterSpacing=0;gradientColor=none;glass=0;fontColor=#FFFFFF;fontSize=12;horizontal=1;" parent="device-layer" vertex="1">
          <mxGeometry x="270" y="40" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="device-data" value="&lt;font style=&quot;color: rgb(255, 255, 255);&quot;&gt;• 注水流量&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(255, 255, 255);&quot;&gt;&lt;span style=&quot;text-align: left;&quot;&gt;• 注水压力&lt;/span&gt;&lt;br&gt;• 注水温度&lt;br&gt;&lt;span style=&quot;text-align: left;&quot;&gt;• 水嘴开度&lt;/span&gt;&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(255, 255, 255);&quot;&gt;• 设备状态&lt;br&gt;• 控制指令&lt;/font&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#877945;strokeColor=#605531;textShadow=0;labelBorderColor=none;labelBackgroundColor=none;strokeWidth=2;arcSize=12;textDirection=vertical-rl;fontColor=#FFFFFF;fontStyle=0;fontSize=14;" parent="device-layer" vertex="1">
          <mxGeometry x="30" y="136" width="340" height="100" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-29" value="" style="group;strokeWidth=3;strokeColor=none;rounded=0;arcSize=50;glass=0;" vertex="1" connectable="0" parent="1">
          <mxGeometry x="1631" y="627" width="294" height="120" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-58" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;strokeWidth=3;startArrow=block;startFill=1;endArrow=classic;strokeColor=#67AB9F;endFill=1;" edge="1" parent="Wam68u_NnZaqKmoTfJ2d-29" source="Wam68u_NnZaqKmoTfJ2d-25">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="147" y="-468" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-25" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;Nginx 负载均衡" style="ellipse;whiteSpace=wrap;html=1;fillColor=#0AAB65;strokeColor=none;spacingBottom=0;spacingTop=0;fontStyle=1;fontSize=14;fontColor=#FFFFFF;" vertex="1" parent="Wam68u_NnZaqKmoTfJ2d-29">
          <mxGeometry width="294" height="120" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-24" value="" style="fontColor=#000000;verticalAlign=top;verticalLabelPosition=bottom;labelPosition=center;align=center;html=1;outlineConnect=0;fillColor=#fa6800;strokeColor=#C73500;gradientDirection=north;strokeWidth=2;shape=mxgraph.networks.load_balancer;" vertex="1" parent="Wam68u_NnZaqKmoTfJ2d-29">
          <mxGeometry x="83" y="32" width="132" height="39.6" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-43" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;strokeColor=#7B1FA2;startArrow=diamondThin;startFill=1;" edge="1" parent="1" source="emqx-broker" target="desktop-client-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="comm-layer" value="数据通信层 (Communication Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#0040BE;strokeColor=#001DBC;fontStyle=1;fontSize=14;glass=0;fillStyle=auto;rounded=0;fontColor=#ffffff;swimlaneLine=1;strokeWidth=3;" parent="1" vertex="1">
          <mxGeometry x="965" y="567" width="400" height="240" as="geometry" />
        </mxCell>
        <mxCell id="emqx-broker" value="EMQX MQTT Broker&#xa;消息代理服务器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1A76EF;strokeColor=#001DBC;arcSize=50;fontColor=#ffffff;strokeWidth=2;glass=0;fontStyle=1" parent="comm-layer" vertex="1">
          <mxGeometry x="97" y="40" width="204" height="55" as="geometry" />
        </mxCell>
        <mxCell id="mqtt-topics" value="&lt;b&gt;MQTT主题订阅/发布：&lt;/b&gt;&lt;br&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• /devices/{id}/status - 设备状态&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• /devices/{id}/heartbeat - 心跳状态&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;/span&gt;&lt;/div&gt;&lt;span style=&quot;&quot;&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• /devices/{id}/data - 数据采集&lt;/span&gt;&lt;/div&gt;&lt;/span&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;&quot;&gt;• /devices/{id}/control - 设备控制&lt;/span&gt;&lt;span style=&quot;text-align: left;&quot;&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;......&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#0040BE;strokeColor=#001DBC;fontColor=#FFFFFF;strokeWidth=2;glass=0;arcSize=11;" parent="comm-layer" vertex="1">
          <mxGeometry x="40" y="110" width="340" height="110" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-21" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="480" y="92" width="348" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-12" value="&lt;font color=&quot;#ffffff&quot;&gt;专线、移动数据、WIFI、卫星通讯等&amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/font&gt;" style="shape=step;perimeter=stepPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=light-dark(#8D64DA,#A475FE);strokeWidth=2;strokeColor=light-dark(#513A7E,#A475FE);fontStyle=1;fontSize=14;align=right;labelBackgroundColor=none;gradientColor=none;rounded=1;size=35;" vertex="1" parent="Wam68u_NnZaqKmoTfJ2d-21">
          <mxGeometry width="348" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-17" value="" style="points=[];aspect=fixed;html=1;align=center;shadow=0;dashed=0;fillColor=#FFFFFF;strokeColor=none;shape=mxgraph.alibaba_cloud.ccn_cloud_connect_network;" vertex="1" parent="Wam68u_NnZaqKmoTfJ2d-21">
          <mxGeometry x="34" y="17.049999999999997" width="56.1" height="45.9" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;strokeColor=#67AB9F;" edge="1" parent="1" target="frontend-layer">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1879.9999999999995" y="189.50000000000023" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-60" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;startArrow=diamondThin;startFill=0;strokeColor=#67AB9F;" edge="1" parent="1" source="Wam68u_NnZaqKmoTfJ2d-25" target="backend-layer">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-65" value="云端租户管理" style="swimlane;whiteSpace=wrap;html=1;fillColor=#647687;strokeColor=#314354;fontStyle=1;fontSize=14;strokeWidth=3;fontColor=#ffffff;" vertex="1" parent="1">
          <mxGeometry x="1882" y="62" width="400" height="255" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-66" value="Vue 3 + Vite&#xa;Web管理平台" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#647687;strokeColor=#314354;strokeWidth=3;fontColor=#ffffff;" vertex="1" parent="Wam68u_NnZaqKmoTfJ2d-65">
          <mxGeometry x="20" y="42" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-67" value="Pinia&#xa;状态管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#647687;strokeColor=#314354;strokeWidth=3;fontColor=#ffffff;" vertex="1" parent="Wam68u_NnZaqKmoTfJ2d-65">
          <mxGeometry x="180" y="38" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-68" value="ECharts&#xa;图表组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#647687;strokeColor=#314354;strokeWidth=3;fontColor=#ffffff;" vertex="1" parent="Wam68u_NnZaqKmoTfJ2d-65">
          <mxGeometry x="302" y="38" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-69" value="Sass/SCSS&#xa;样式系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#647687;strokeColor=#314354;strokeWidth=3;fontColor=#ffffff;" vertex="1" parent="Wam68u_NnZaqKmoTfJ2d-65">
          <mxGeometry x="180" y="88" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-70" value="Element Plus&#xa;UI组件库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#647687;strokeColor=#314354;strokeWidth=3;fontColor=#ffffff;" vertex="1" parent="Wam68u_NnZaqKmoTfJ2d-65">
          <mxGeometry x="302" y="88" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-71" value="&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 租户管理&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 租户功能权限&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;• 租户套餐&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;• 租户消息通知（全局、针对商户、针对商户设备等）&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;br&gt;&lt;/span&gt;•&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;•&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;•&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#647687;strokeColor=#314354;strokeWidth=3;fontColor=#ffffff;" vertex="1" parent="Wam68u_NnZaqKmoTfJ2d-65">
          <mxGeometry x="17" y="140" width="366" height="98" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
